import './global.css';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { Suspense, useCallback, useEffect } from 'react';
import BaseApp from '@navigation/navigation';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SQLiteProvider } from 'expo-sqlite';
import { ActivityIndicator } from 'react-native';
import { useDrizzleStudio } from 'expo-drizzle-studio-plugin';
import { initTable } from '@services/db/migration';
import { DATABASE_NAME, sqliteDB } from '@services/db/db';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat'
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';
import Instalog from '@instalog.dev/expo';

dayjs.extend(advancedFormat);

// This is the default configuration
configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false, // Reanimated runs in strict mode by default
});

SplashScreen.preventAutoHideAsync().catch(() => {});
const apiKey = "instalog_7462152760224b0286eb048a9712adbe";


export default function App() {
  const [fontsLoaded] = useFonts({
    'CircularStd-Light': require('@assets/fonts/CircularStd-Light.otf'),
    'CircularStd-Book': require('@assets/fonts/CircularStd-Book.otf'),
    'CircularStd-Medium': require('@assets/fonts/CircularStd-Medium.otf'),
    'CircularStd-Bold': require('@assets/fonts/CircularStd-Bold.otf'),
    'CircularStd-Black': require('@assets/fonts/CircularStd-Black.otf'),
  });

  useEffect(() => {
    const initialize = async () => {
      await Instalog.initialize(apiKey, {
        isLogEnabled: true,
        isCrashEnabled: true,
        isFeedbackEnabled: true,
        isLoggerEnabled: true,
        autoCaptureCrashes: true,
      });
      // await Instalog.identifyUser("expo_test_user");
    };
    initialize();
  }, []);


  useEffect(() => {
    async function initDB() {
      try {
        // create tables to ensure database tables exist
        await initTable();
      } catch (error) {
        console.error('Failed to initialize database:', error);
      }
    }

    initDB();
  }, []);

  useDrizzleStudio(sqliteDB);

  const onLayoutRootView = useCallback(async () => {
    if (fontsLoaded) {
      try {
        await SplashScreen.hideAsync();
      } catch (e) {
        console.error('Error hiding splash screen:', e);
      }
    }
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }} onLayout={onLayoutRootView}>
      <Suspense fallback={<ActivityIndicator size="large" />}>
        <SQLiteProvider databaseName={DATABASE_NAME} options={{ enableChangeListener: true }} useSuspense>
          <BaseApp />
        </SQLiteProvider>
      </Suspense>
    </GestureHandlerRootView>
  );
}
