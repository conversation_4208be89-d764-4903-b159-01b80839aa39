// Enum for the different mood variants
export enum MOOD_VARIANT {
  NERVOUS = "Nervous",
  MELT = "Melt",
  NEUTRAL = "Neutral",
  CONFUSED = "Confused",
  CRY = "Cry",
  ANGRY = "Angry",
  SAD = "Sad",
  HAPPY = "Happy",
  SMILE = "Smile",
  NONE = "None"
}

export const MOOD_VALUES = [
  'Nervous',
  'Melt',
  'Neutral',
  'Confused',
  'Cry',
  'Angry',
  'Sad',
  'Happy',
  'Smile',
  'None'
] as const;

// Enum for mood activation states
export enum MOOD_STATE {
  ACTIVE = "On",
  INACTIVE = "Off"
}

export interface JournalEntryType {
  id: string;
  mood: MOOD_VARIANT;
  title: string;
  content: string;
  images: string[];
  created_date: string;
  updated_date: string;
}

export const isMood = (val: any): val is MOOD_VARIANT =>
  MOOD_VALUES.includes(val as MOOD_VARIANT);