import { StackNavigationProp } from '@react-navigation/stack';

export type RootStackParamList = {
  HomeScreen: undefined;
  SettingsScreen: undefined;
  DashboardScreen: undefined;
  Settings: undefined;
  WriteJournalScreen: { journalId?: string; images?: string[]; title?: string; entry_date?: string } | undefined;
};

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}

    // Add this type definition
    interface NavigationProp<ParamList = RootStackParamList, RouteName extends keyof ParamList = keyof ParamList>
      extends StackNavigationProp<ParamList, RouteName> {}
  }
}
