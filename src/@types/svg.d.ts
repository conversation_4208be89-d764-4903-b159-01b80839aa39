// declare module '*.svg' {
//   import * as React from 'react';
//   import {SvgProps} from 'react-native-svg';
//   const content: React.FC<SvgProps>;
//   export default content;
// }

declare module "*.svg" {
  import React from "react";
  import { SvgProps } from "react-native-svg";
  const content: React.FC<
    SvgProps & {
      size?: number;
      color?: string;
      primaryColor?: string;
      secondaryColor?: string;
    }
  >;
  export default content;
}
