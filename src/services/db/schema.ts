import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { nanoid } from 'nanoid/non-secure';
import { MOOD_VALUES, MOOD_VARIANT } from 'src/@types/app-interface';
// Import your existing MOOD_VARIANT type


// Define journal entry schema
export const journalEntries = sqliteTable('journal_entries', {
  id: text('id').primaryKey().$defaultFn(() => nanoid()),
  mood: text('mood', { enum: MOOD_VALUES }).notNull(),
  // mood: text('mood', { enum: Object.values(MOOD_VARIANT) }).notNull(),
  entry_date: text('entry_date').notNull(),
  title: text('title').notNull(),
  content: text('content').notNull(),
  images: text('images', { mode: 'json' }).$type<string[]>().default([]),
  bookmarked: integer('bookmarked').notNull().default(0).$type<boolean>(),
  location: text('location', { mode: 'json' })
  .$type<{
    latitude: number;
    longitude: number;
    address: string;
    name: string;
  } | null>() // <== This allows null
  .default(null),
  created_date: text('created_date').notNull().$defaultFn(() => new Date().toISOString()),
  updated_date: text('updated_date').notNull().$defaultFn(() => new Date().toISOString()),
});

// Interface matching our schema
export interface JournalEntry {
  id: string;
  mood: MOOD_VARIANT;
  title: string;
  content: string;
  entry_date: string; // Date the journal entry is about
  images: string[];
  bookmarked: boolean;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    name: string;
  } | null;
  // created_date: string; // Date the entry was created in the app
  // updated_date: string;
}

// Drizzle automatically maps the schema to this interface
export type InsertJournalEntry = typeof journalEntries.$inferInsert;
export type SelectJournalEntry = typeof journalEntries.$inferSelect;
