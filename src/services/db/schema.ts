import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { nanoid } from 'nanoid/non-secure';
import { MOOD_VALUES, MOOD_VARIANT } from 'src/@types/app-interface';
// Import your existing MOOD_VARIANT type


// Define journal entry schema
export const journalEntries = sqliteTable('journal_entries', {
  id: text('id').primaryKey().$defaultFn(() => nanoid()),
  mood: text('mood', { enum: MOOD_VALUES }).notNull(),
  // mood: text('mood', { enum: Object.values(MOOD_VARIANT) }).notNull(),
  entry_date: text('entry_date').notNull(),
  title: text('title').notNull(),
  content: text('content').notNull(),
  images: text('images', { mode: 'json' }).$type<string[]>().default([]),
  bookmarked: integer('bookmarked').notNull().default(0).$type<boolean>(),
  location: text('location', { mode: 'json' })
  .$type<{
    latitude: number;
    longitude: number;
    address: string;
    name: string;
  } | null>() // <== This allows null
  .default(null),
  created_date: text('created_date').notNull().$defaultFn(() => new Date().toISOString()),
  updated_date: text('updated_date').notNull().$defaultFn(() => new Date().toISOString()),
});

// Interface matching our schema
export interface JournalEntry {
  id: string;
  mood: MOOD_VARIANT;
  title: string;
  content: string;
  entry_date: string; // Date the journal entry is about
  images: string[];
  bookmarked: boolean;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    name: string;
  } | null;
  // created_date: string; // Date the entry was created in the app
  // updated_date: string;
}

// Define app config schema
export const appConfig = sqliteTable('app_config', {
  id: text('id').primaryKey().$defaultFn(() => nanoid()),
  user_id: text('user_id').notNull().$defaultFn(() => nanoid()),
  device_id: text('device_id').notNull(),
  device_name: text('device_name').notNull(),
  first_launch_date: text('first_launch_date').notNull().$defaultFn(() => new Date().toISOString()),
  last_launch_date: text('last_launch_date').notNull().$defaultFn(() => new Date().toISOString()),
  icloud_backup_enable: integer('icloud_backup_enable').notNull().default(0).$type<boolean>(),
});

// Interface for app config
export interface AppConfig {
  id: string;
  user_id: string;
  device_id: string;
  device_name: string;
  first_launch_date: string;
  last_launch_date: string;
  icloud_backup_enable: boolean;
}

// Drizzle automatically maps the schema to this interface
export type InsertJournalEntry = typeof journalEntries.$inferInsert;
export type SelectJournalEntry = typeof journalEntries.$inferSelect;
export type InsertAppConfig = typeof appConfig.$inferInsert;
export type SelectAppConfig = typeof appConfig.$inferSelect;
