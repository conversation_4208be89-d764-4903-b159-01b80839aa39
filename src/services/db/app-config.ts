import { eq } from 'drizzle-orm';
import { getDB } from './db';
import { appConfig, InsertAppConfig, SelectAppConfig } from './schema';
import { nanoid } from 'nanoid/non-secure';
import { Platform } from 'react-native';

const db = getDB();

/**
 * Get the app configuration (should only be one record)
 */
export async function getAppConfig(): Promise<SelectAppConfig | undefined> {
  const configs = await db.select().from(appConfig).limit(1);
  return configs[0];
}

/**
 * Create initial app configuration with device info
 */
export async function createAppConfig(deviceId?: string, deviceName?: string): Promise<SelectAppConfig> {
  const now = new Date().toISOString();

  // Get device information with fallbacks
  const finalDeviceId = deviceId || nanoid();
  const finalDeviceName = deviceName || `${Platform.OS} Device`;

  const configData: InsertAppConfig = {
    user_id: nanoid(), // Generate unique user ID
    device_id: finalDeviceId,
    device_name: finalDeviceName,
    first_launch_date: now,
    last_launch_date: now,
    icloud_backup_enable: false,
  };

  const inserted = await db.insert(appConfig).values(configData).returning();
  return inserted[0];
}

/**
 * Update last launch date
 */
export async function updateLastLaunchDate(id: string): Promise<SelectAppConfig | undefined> {
  const now = new Date().toISOString();
  
  const updated = await db
    .update(appConfig)
    .set({ last_launch_date: now })
    .where(eq(appConfig.id, id))
    .returning();

  return updated[0];
}

/**
 * Update iCloud backup setting
 */
export async function updateICloudBackupSetting(id: string, enabled: boolean): Promise<SelectAppConfig | undefined> {
  const updated = await db
    .update(appConfig)
    .set({ icloud_backup_enable: enabled })
    .where(eq(appConfig.id, id))
    .returning();

  return updated[0];
}

/**
 * Get or create app configuration
 * This ensures we always have a config record and handles first-time setup
 */
export async function getOrCreateAppConfig(): Promise<SelectAppConfig> {
  let config = await getAppConfig();
  
  if (!config) {
    // First time launch - create new config
    config = await createAppConfig();
  } else {
    // Update last launch date
    config = await updateLastLaunchDate(config.id) || config;
  }
  
  return config;
}
