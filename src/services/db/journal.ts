import { eq, desc, and, gte, lt, or, sql, inArray } from 'drizzle-orm';
import { getDB } from './db';
import { journalEntries, InsertJournalEntry, SelectJournalEntry } from './schema';
import { MOOD_VARIANT } from 'src/@types/app-interface';

const db = getDB();

// Get today's date boundaries
const today = new Date();
today.setHours(0, 0, 0, 0); // Start of today (midnight)
const todayISO = today.toISOString();

const tomorrow = new Date(today);
tomorrow.setDate(tomorrow.getDate() + 1); // Start of tomorrow
const tomorrowISO = tomorrow.toISOString();

// Create a new journal entry
export async function createJournalEntry(entry: InsertJournalEntry): Promise<SelectJournalEntry> {
  const now = new Date().toISOString();

  const entryData: InsertJournalEntry = {
    ...entry,
    created_date: now,
    updated_date: now,
  };

  const inserted = await db.insert(journalEntries).values(entryData).returning();
  return inserted[0];
}

// Get all journal entries with optional pagination
export async function getPaginatedJournalEntries(page?: number, pageSize?: number): Promise<SelectJournalEntry[]> {
  let query = db.select().from(journalEntries).orderBy(desc(journalEntries.updated_date));

  if (pageSize && pageSize > 0) {
    const currentPage = page && page > 0 ? page : 1;
    const offset = (currentPage - 1) * pageSize;
    query = query.limit(pageSize).offset(offset);
  }

  return await query;
}

/**
 * Get all journal entries
 */
export async function getAllJournalEntries(): Promise<SelectJournalEntry[]> {
  const entries = await db.select().from(journalEntries).all();
  return entries;
}

/**
 * Get all todays journal entries
 */
export async function getAllTodayEntries(): Promise<SelectJournalEntry[]> {
  const entries = await db
    .select()
    .from(journalEntries)
    .where(and(gte(journalEntries.created_date, todayISO), lt(journalEntries.created_date, tomorrowISO)));
  return entries;
}

// Get a single journal entry by ID
export async function getJournalEntryById(id: string): Promise<SelectJournalEntry | undefined> {
  const entries = await db.select().from(journalEntries).where(eq(journalEntries.id, id));
  return entries[0];
}

// Update a journal entry
export async function updateJournalEntry(
  id: string,
  updates: Partial<InsertJournalEntry>,
): Promise<SelectJournalEntry | undefined> {
  const updateData = {
    ...updates,
    updated_date: new Date().toISOString(),
  };

  const updated = await db.update(journalEntries).set(updateData).where(eq(journalEntries.id, id)).returning();

  return updated[0];
}

// Updated database function to delete multiple journal entries
export async function deleteJournalEntries(ids: string[]): Promise<boolean> {
  try {
    if (ids.length === 0) return true;
    
    // Use inArray for multiple IDs or single delete for one ID
    if (ids.length === 1) {
      await db.delete(journalEntries).where(eq(journalEntries.id, ids[0]));
    } else {
      await db.delete(journalEntries).where(inArray(journalEntries.id, ids));
    }
    
    return true;
  } catch (error) {
    console.error('Failed to delete journal entries:', error);
    return false;
  }
}

export async function deleteJournalEntry(id: string): Promise<boolean> {
  return await deleteJournalEntries([id]);
}

// Filter journal entries by mood
export async function getJournalEntriesByMood(mood: MOOD_VARIANT): Promise<SelectJournalEntry[]> {
  return await db
    .select()
    .from(journalEntries)
    .where(eq(journalEntries.mood, mood))
    .orderBy(desc(journalEntries.updated_date));
}

// Filter journal entries by mood
export async function getBookmarkedJournalEntries(): Promise<SelectJournalEntry[]> {
  return await db
    .select()
    .from(journalEntries)
    .where(eq(journalEntries.bookmarked, true))
    .orderBy(desc(journalEntries.updated_date));
}

// Search journal entries by title or content
export async function searchJournalEntries(searchText: string): Promise<SelectJournalEntry[]> {
  // Create the search pattern with wildcards
  const searchPattern = `%${searchText}%`;
  
  try {
    // Use the query builder pattern instead of template literals
    const result = await db
      .select()
      .from(journalEntries)
      .where(
        or(
          sql`title LIKE ${searchPattern}`,
          sql`content LIKE ${searchPattern}`
        )
      )
      .orderBy(desc(journalEntries.updated_date));
    
    return result as SelectJournalEntry[];
  } catch (error) {
    console.error('Search query error:', error);
    throw error;
  }
}
