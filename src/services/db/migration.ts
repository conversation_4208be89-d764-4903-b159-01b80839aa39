import { closeDB, getDB } from './db';
import { delay } from '@utils/functions';

export async function initTable() {
  const db = getDB();
  try {
    await db.run(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id TEXT PRIMARY KEY,
        mood TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        entry_date TEXT NOT NULL,
        images TEXT DEFAULT '[]',
        location TEXT DEFAULT NULL,
        bookmarked INTEGER NOT NULL DEFAULT 0,
        created_date TEXT NOT NULL,
        updated_date TEXT NOT NULL
      );
    `);
    console.log('Migrations completed successfully');
    return true;
  } catch (error) {
    console.error('Migration failed: ', error);
    return false;
  }
}

// Sample journal entries for initial data
const sampleJournalEntries = [
  {
    id: 'e01',
    mood: 'Happy',
    title: 'Great day at the park',
    content: 'Spent the whole afternoon at the park. The weather was perfect and I met some interesting people.',
    entry_date: '2024-11-03',
    images: '[]',
    location: 'Central Park',
    bookmarked: 1,
    created_date: '2024-11-03T14:23:00',
    updated_date: '2024-11-03T14:23:00'
  },
  {
    id: 'e02',
    mood: 'Sad',
    title: 'Missing home',
    content: 'Feeling homesick today. Called my parents but it only made me miss them more.',
    entry_date: '2024-11-01',
    images: '[]',
    location: 'My Apartment',
    bookmarked: 0,
    created_date: '2024-11-01T22:15:00',
    updated_date: '2024-11-01T22:30:00'
  },
  {
    id: 'e03',
    mood: 'Smile',
    title: 'New project kickoff',
    content: 'Got assigned to lead the new mobile app project at work! Can\'t wait to get started.',
    entry_date: '2024-10-29',
    images: '[]',
    location: 'Office',
    bookmarked: 1,
    created_date: '2024-10-29T17:45:00',
    updated_date: '2024-10-29T18:00:00'
  },
  {
    id: 'e04',
    mood: 'Nervous',
    title: 'Big presentation tomorrow',
    content: 'Can\'t sleep thinking about the presentation. Hope I don\'t mess it up.',
    entry_date: '2024-10-25',
    images: '[]',
    location: 'Home',
    bookmarked: 0,
    created_date: '2024-10-25T23:50:00',
    updated_date: '2024-10-25T23:55:00'
  },
  {
    id: 'e05',
    mood: 'Neutral',
    title: 'Meditation session',
    content: 'Had my first guided meditation session today. Feeling incredibly peaceful.',
    entry_date: '2024-10-22',
    images: '["meditation.jpg"]',
    location: 'Yoga Studio',
    bookmarked: 0,
    created_date: '2024-10-22T19:30:00',
    updated_date: '2024-10-22T19:45:00'
  },
  {
    id: 'e06',
    mood: 'Angry',
    title: 'Debugging all day',
    content: 'Spent the entire day trying to fix one bug. Still not solved.',
    entry_date: '2024-10-18',
    images: '[]',
    location: 'Home Office',
    bookmarked: 0,
    created_date: '2024-10-18T21:10:00',
    updated_date: '2024-10-18T21:20:00'
  },
  {
    id: 'e07',
    mood: 'Smile',
    title: 'Finished my painting',
    content: 'Finally completed the painting I\'ve been working on for weeks. Really happy with how it turned out.',
    entry_date: '2024-10-15',
    images: '["painting.jpg"]',
    location: 'Art Studio',
    bookmarked: 1,
    created_date: '2024-10-15T16:20:00',
    updated_date: '2024-10-15T16:30:00'
  },
  {
    id: 'e08',
    mood: 'Angry',
    title: 'Car broke down',
    content: 'Car died in the middle of nowhere. Had to wait hours for a tow truck.',
    entry_date: '2024-10-12',
    images: '[]',
    location: 'Highway 101',
    bookmarked: 0,
    created_date: '2024-10-12T14:50:00',
    updated_date: '2024-10-12T15:00:00'
  },
  {
    id: 'e09',
    mood: 'Happy',
    title: 'Surprise visit',
    content: 'My best friend showed up at my door unexpectedly. Best surprise ever!',
    entry_date: '2024-10-08',
    images: '["friends.jpg"]',
    location: 'Home',
    bookmarked: 1,
    created_date: '2024-10-08T20:45:00',
    updated_date: '2024-10-08T21:00:00'
  },
  {
    id: 'e10',
    mood: 'Neutral',
    title: 'Nothing special today',
    content: 'Just an ordinary day. Work, gym, dinner, sleep.',
    entry_date: '2024-10-05',
    images: '[]',
    location: null,
    bookmarked: 0,
    created_date: '2024-10-05T22:00:00',
    updated_date: '2024-10-05T22:10:00'
  },
  {
    id: 'e11',
    mood: 'Smile',
    title: 'Ted Talk on innovation',
    content: 'Watched an amazing Ted Talk today. Got so many ideas for my next project.',
    entry_date: '2024-10-01',
    images: '[]',
    location: 'Living Room',
    bookmarked: 1,
    created_date: '2024-10-01T21:15:00',
    updated_date: '2024-10-01T21:30:00'
  },
  {
    id: 'e12',
    mood: 'Melt',
    title: 'Marathon training',
    content: 'Did my longest run yet preparing for the marathon. 18 miles and I\'m completely drained.',
    entry_date: '2024-09-28',
    images: '["run_map.jpg"]',
    location: 'City Trail',
    bookmarked: 0,
    created_date: '2024-09-28T18:30:00',
    updated_date: '2024-09-28T18:45:00'
  },
  {
    id: 'e13',
    mood: 'Confused',
    title: 'Life decisions',
    content: 'Not sure if I should take that job offer or stay where I am. Pro/con lists aren\'t helping.',
    entry_date: '2024-09-25',
    images: '[]',
    location: 'Coffee Shop',
    bookmarked: 0,
    created_date: '2024-09-25T15:20:00',
    updated_date: '2024-09-25T15:40:00'
  },
  {
    id: 'e14',
    mood: 'Happy',
    title: 'Fresh start',
    content: 'First day in the new apartment. It feels like a fresh beginning.',
    entry_date: '2024-09-22',
    images: '["new_apartment.jpg", "view.jpg"]',
    location: 'New Apartment',
    bookmarked: 1,
    created_date: '2024-09-22T19:10:00',
    updated_date: '2024-09-22T19:25:00'
  },
  {
    id: 'e15',
    mood: 'Sad',
    title: 'Movie letdown',
    content: 'Was really looking forward to that new sci-fi movie, but it was a total letdown.',
    entry_date: '2024-09-18',
    images: '[]',
    location: 'Cinema',
    bookmarked: 0,
    created_date: '2024-09-18T23:45:00',
    updated_date: '2024-09-19T00:00:00'
  },
  {
    id: 'e16',
    mood: 'Smile',
    title: 'Beach day',
    content: 'Spent the day at the beach just listening to the waves. Exactly what I needed.',
    entry_date: '2024-09-15',
    images: '["sunset.jpg"]',
    location: 'Malibu Beach',
    bookmarked: 1,
    created_date: '2024-09-15T20:30:00',
    updated_date: '2024-09-15T20:45:00'
  },
  {
    id: 'e17',
    mood: 'Melt',
    title: 'Too many deadlines',
    content: 'Feeling buried under deadlines. Don\'t even know where to start.',
    entry_date: '2024-09-12',
    images: '[]',
    location: 'Office',
    bookmarked: 0,
    created_date: '2024-09-12T16:50:00',
    updated_date: '2024-09-12T17:00:00'
  },
  {
    id: 'e18',
    mood: 'Happy',
    title: 'Comedy night',
    content: 'Went to a comedy club with coworkers. Haven\'t laughed that hard in ages.',
    entry_date: '2024-09-08',
    images: '[]',
    location: 'Laugh Factory',
    bookmarked: 0,
    created_date: '2024-09-08T23:15:00',
    updated_date: '2024-09-08T23:30:00'
  },
  {
    id: 'e19',
    mood: 'Nervous',
    title: 'Blind date',
    content: 'Going on a blind date in an hour. Why did I agree to this?',
    entry_date: '2024-09-05',
    images: '["outfit.jpg"]',
    location: 'Home',
    bookmarked: 0,
    created_date: '2024-09-05T18:00:00',
    updated_date: '2024-09-05T18:15:00'
  },
  {
    id: 'e20',
    mood: 'Happy',
    title: 'Unexpected bonus',
    content: 'Got a surprise bonus at work today! Already planning how to spend it.',
    entry_date: '2024-09-01',
    images: '[]',
    location: 'Office',
    bookmarked: 1,
    created_date: '2024-09-01T17:30:00',
    updated_date: '2024-09-01T17:45:00'
  },
  {
    id: 'e21',
    mood: 'Cry',
    title: 'Rainy Sunday',
    content: 'Rainy day, old music, and memories. Sometimes sadness is comforting in its own way.',
    entry_date: '2024-08-28',
    images: '[]',
    location: 'Living Room',
    bookmarked: 0,
    created_date: '2024-08-28T15:40:00',
    updated_date: '2024-08-28T16:00:00'
  },
  {
    id: 'e22',
    mood: 'Smile',
    title: 'Morning jog',
    content: 'Woke up early and went for a jog. Feel like I can conquer the world today!',
    entry_date: '2024-08-25',
    images: '[]',
    location: 'Apartment',
    bookmarked: 0,
    created_date: '2024-08-25T18:00:00',
    updated_date: '2024-08-25T18:15:00'
  },
  {
    id: 'e23',
    mood: 'Neutral',
    title: 'Cleaning spree',
    content: 'Spent the day cleaning and reorganizing. Feels good to have a tidy space.',
    entry_date: '2024-08-25',
    images: '[]',
    location: 'Apartment',
    bookmarked: 0,
    created_date: '2024-08-25T18:00:00',
    updated_date: '2024-08-25T18:15:00'
  },
  {
    id: 'e24',
    mood: 'Angry',
    title: 'Lost wallet',
    content: 'Lost my wallet on the subway. Cancelling everything now.',
    entry_date: '2024-08-22',
    images: '[]',
    location: 'Subway',
    bookmarked: 0,
    created_date: '2024-08-22T17:20:00',
    updated_date: '2024-08-22T17:35:00'
  },
  {
    id: 'e25',
    mood: 'Smile',
    title: 'Baking experiment',
    content: 'Tried baking sourdough for the first time. Not perfect, but proud.',
    entry_date: '2024-08-18',
    images: '["bread.jpg"]',
    location: 'Kitchen',
    bookmarked: 1,
    created_date: '2024-08-18T14:00:00',
    updated_date: '2024-08-18T14:20:00'
  },
  {
    id: 'e26',
    mood: 'Happy',
    title: 'Picnic with friends',
    content: 'Spent the afternoon at the park with close friends and good food.',
    entry_date: '2024-08-10',
    images: '["picnic.jpg"]',
    location: 'Central Park',
    bookmarked: 0,
    created_date: '2024-08-10T15:00:00',
    updated_date: '2024-08-10T15:15:00'
  },
  {
    id: 'e27',
    mood: 'Sad',
    title: 'Bad news from home',
    content: 'Got some worrying news from home. Feeling helpless and anxious.',
    entry_date: '2024-08-05',
    images: '[]',
    location: 'Living Room',
    bookmarked: 0,
    created_date: '2024-08-05T20:00:00',
    updated_date: '2024-08-05T20:30:00'
  },
  {
    id: 'e28',
    mood: 'Relaxed',
    title: 'Beach walk',
    content: 'Evening walk on the beach. The sound of waves is calming.',
    entry_date: '2024-07-28',
    images: '["beach.jpg"]',
    location: 'Seaside',
    bookmarked: 1,
    created_date: '2024-07-28T19:00:00',
    updated_date: '2024-07-28T19:30:00'
  },
  {
    id: 'e29',
    mood: 'Grateful',
    title: 'Old friend visit',
    content: 'An old friend visited today. So much laughter and shared memories.',
    entry_date: '2024-07-20',
    images: '["friends.jpg"]',
    location: 'My Apartment',
    bookmarked: 1,
    created_date: '2024-07-20T18:00:00',
    updated_date: '2024-07-20T18:25:00'
  },
  {
    id: 'e30',
    mood: 'Tired',
    title: 'All-nighter',
    content: 'Pulled an all-nighter to finish the project. Totally drained.',
    entry_date: '2024-07-15',
    images: '[]',
    location: 'Workspace',
    bookmarked: 0,
    created_date: '2024-07-15T06:30:00',
    updated_date: '2024-07-15T06:45:00'
  },
  {
    id: 'e31',
    mood: 'Happy',
    title: 'Watched favorite movie',
    content: 'Rewatched my favorite movie. Still hits all the right spots.',
    entry_date: '2024-07-10',
    images: '["movie-night.jpg"]',
    location: 'Living Room',
    bookmarked: 1,
    created_date: '2024-07-10T21:00:00',
    updated_date: '2024-07-10T21:10:00'
  },
  {
    id: 'e32',
    mood: 'Anxious',
    title: 'Waiting on results',
    content: 'Still waiting for test results. Trying to stay calm.',
    entry_date: '2024-07-03',
    images: '[]',
    location: 'Bedroom',
    bookmarked: 0,
    created_date: '2024-07-03T16:00:00',
    updated_date: '2024-07-03T16:10:00'
  },
  {
    id: 'e33',
    mood: 'Calm',
    title: 'Reading day',
    content: 'Spent the entire day reading. Escaped into a different world.',
    entry_date: '2024-06-27',
    images: '["book.jpg"]',
    location: 'Garden',
    bookmarked: 1,
    created_date: '2024-06-27T12:00:00',
    updated_date: '2024-06-27T12:20:00'
  },
  {
    id: 'e34',
    mood: 'Happy',
    title: 'Date night',
    content: 'Went on a lovely dinner date. Feeling cherished.',
    entry_date: '2024-06-20',
    images: '["dinner.jpg"]',
    location: 'City Center',
    bookmarked: 1,
    created_date: '2024-06-20T21:00:00',
    updated_date: '2024-06-20T21:15:00'
  },
  {
    id: 'e35',
    mood: 'Bored',
    title: 'Nothing day',
    content: 'Did absolutely nothing today. Felt weird but okay.',
    entry_date: '2024-06-15',
    images: '[]',
    location: 'Home',
    bookmarked: 0,
    created_date: '2024-06-15T18:00:00',
    updated_date: '2024-06-15T18:05:00'
  },
  {
    id: 'e36',
    mood: 'Joyful',
    title: 'Got a pet',
    content: 'Adopted a puppy! I’m already in love.',
    entry_date: '2024-06-10',
    images: '["puppy.jpg"]',
    location: 'Pet Store',
    bookmarked: 1,
    created_date: '2024-06-10T13:00:00',
    updated_date: '2024-06-10T13:25:00'
  },
  {
    id: 'e37',
    mood: 'Content',
    title: 'Slow morning',
    content: 'Made coffee and sat quietly for an hour. Peaceful start.',
    entry_date: '2024-06-04',
    images: '["coffee.jpg"]',
    location: 'Balcony',
    bookmarked: 1,
    created_date: '2024-06-04T08:00:00',
    updated_date: '2024-06-04T08:15:00'
  },
  {
    id: 'e38',
    mood: 'Excited',
    title: 'Planning a trip',
    content: 'Started planning for the September getaway. Pumped!',
    entry_date: '2024-05-29',
    images: '[]',
    location: 'Bedroom',
    bookmarked: 1,
    created_date: '2024-05-29T17:00:00',
    updated_date: '2024-05-29T17:20:00'
  },
  {
    id: 'e39',
    mood: 'Lonely',
    title: 'Quiet Sunday',
    content: 'Didn’t talk to anyone today. Felt a bit alone.',
    entry_date: '2024-05-26',
    images: '[]',
    location: 'Home',
    bookmarked: 0,
    created_date: '2024-05-26T21:00:00',
    updated_date: '2024-05-26T21:10:00'
  },
  {
    id: 'e40',
    mood: 'Cheerful',
    title: 'Dance class',
    content: 'Tried a beginner salsa class. So much fun!',
    entry_date: '2024-05-21',
    images: '["salsa.jpg"]',
    location: 'Studio 5',
    bookmarked: 1,
    created_date: '2024-05-21T18:30:00',
    updated_date: '2024-05-21T18:45:00'
  },
  {
    id: 'e41',
    mood: 'Productive',
    title: 'Organized closet',
    content: 'Spent hours organizing clothes by color and season.',
    entry_date: '2024-05-15',
    images: '["closet.jpg"]',
    location: 'Bedroom',
    bookmarked: 1,
    created_date: '2024-05-15T14:00:00',
    updated_date: '2024-05-15T14:30:00'
  },
  {
    id: 'e42',
    mood: 'Worried',
    title: 'Car trouble',
    content: 'Car broke down again. Might need a new one.',
    entry_date: '2024-05-10',
    images: '[]',
    location: 'Highway',
    bookmarked: 0,
    created_date: '2024-05-10T13:00:00',
    updated_date: '2024-05-10T13:10:00'
  },
  {
    id: 'e43',
    mood: 'Peaceful',
    title: 'Rainy day',
    content: 'Listened to the rain while journaling. Therapeutic.',
    entry_date: '2024-05-05',
    images: '["rain.jpg"]',
    location: 'Home Office',
    bookmarked: 1,
    created_date: '2024-05-05T10:00:00',
    updated_date: '2024-05-05T10:20:00'
  },
  {
    id: 'e44',
    mood: 'Inspired',
    title: 'Watched a documentary',
    content: 'Watched a doc on climate change. I want to do more.',
    entry_date: '2024-04-30',
    images: '["doc.jpg"]',
    location: 'Living Room',
    bookmarked: 1,
    created_date: '2024-04-30T20:00:00',
    updated_date: '2024-04-30T20:30:00'
  },
  {
    id: 'e45',
    mood: 'Frustrated',
    title: 'Internet outage',
    content: 'Internet has been down all day. Work delayed.',
    entry_date: '2024-04-27',
    images: '[]',
    location: 'Home Office',
    bookmarked: 0,
    created_date: '2024-04-27T12:00:00',
    updated_date: '2024-04-27T12:05:00'
  },
  {
    id: 'e46',
    mood: 'Calm',
    title: 'Yoga morning',
    content: 'Started day with stretching and yoga. Body feels light.',
    entry_date: '2024-04-22',
    images: '["yoga.jpg"]',
    location: 'Living Room',
    bookmarked: 1,
    created_date: '2024-04-22T07:00:00',
    updated_date: '2024-04-22T07:15:00'
  },
  {
    id: 'e47',
    mood: 'Happy',
    title: 'New recipe',
    content: 'Cooked Thai curry for the first time. Delicious!',
    entry_date: '2024-04-16',
    images: '["thai-curry.jpg"]',
    location: 'Kitchen',
    bookmarked: 1,
    created_date: '2024-04-16T19:00:00',
    updated_date: '2024-04-16T19:20:00'
  },
  {
    id: 'e48',
    mood: 'Lonely',
    title: 'Missing family',
    content: 'It’s been months since I saw family. Hope to visit soon.',
    entry_date: '2024-04-10',
    images: '[]',
    location: 'Bedroom',
    bookmarked: 0,
    created_date: '2024-04-10T22:00:00',
    updated_date: '2024-04-10T22:15:00'
  },
  {
    id: 'e49',
    mood: 'Excited',
    title: 'Concert night',
    content: 'Went to see my favorite band live. Incredible energy!',
    entry_date: '2024-04-03',
    images: '["concert.jpg"]',
    location: 'Stadium',
    bookmarked: 1,
    created_date: '2024-04-03T23:00:00',
    updated_date: '2024-04-03T23:30:00'
  },
  {
    id: 'e50',
    mood: 'Hopeful',
    title: 'Looking ahead',
    content: 'Things have been rough, but I feel like brighter days are ahead.',
    entry_date: '2024-03-30',
    images: '[]',
    location: 'Porch',
    bookmarked: 1,
    created_date: '2024-03-30T17:30:00',
    updated_date: '2024-03-30T17:45:00'
  }
];
