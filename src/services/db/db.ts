// db/index.ts
import { drizzle } from 'drizzle-orm/expo-sqlite';
import { openDatabaseSync, SQLiteDatabase } from 'expo-sqlite';
import * as schema from './schema';

export const DATABASE_NAME = 'journal.db';

// Function to initialize the database
export let dbInstance: ReturnType<typeof drizzle> | null = null;
export let sqliteDB: SQLiteDatabase | null = null;

export function getDB() {
  if (!dbInstance) {
    // Open or create the database
    sqliteDB = openDatabaseSync(DATABASE_NAME, { enableChangeListener: true, useNewConnection: true });
    dbInstance = drizzle(sqliteDB, { schema });
  }
  return dbInstance;
}

// Function to close the database connection
export async function closeDB() {
  if (sqliteDB) {
    await sqliteDB.closeAsync();
    sqliteDB.closeSync();
    dbInstance = null;
    sqliteDB = null;
  }
}