const MAPBOX_ACCESS_TOKEN =
  'pk.eyJ1Ijoib2x1d2FmZXJhbm1pIiwiYSI6ImNseXA0dnp1ajBsMHgybnBsMXM2aGNmNncifQ.SAjDLHG7l1HbIipUbe0mcw'; // Move this to .env file
const MAPBOX_SEARCHBOX_API_URL = 'https://api.mapbox.com/search/searchbox/v1/forward';

export type SearchboxCategory =
  | 'restaurant'
  | 'cafe'
  | 'bar'
  | 'hotel'
  | 'airport'
  | 'hospital'
  | 'park'
  | 'shopping'
  | 'museum'
  | 'cinema'
  | 'theater'
  | 'gym'
  | 'pharmacy'
  | 'parking'
  | 'bank'
  | 'school'
  | 'university';

export interface SearchboxOptions {
  proximity?: { latitude: number; longitude: number };
  limit?: number;
  language?: string[];
  country?: string[];
  bbox?: [number, number, number, number]; // [min_lon, min_lat, max_lon, max_lat]
  worldview?: 'us' | 'cn' | 'in';
}

interface RoutablePoint {
  name: string;
  latitude: number;
  longitude: number;
}

interface Coordinates {
  latitude: number;
  longitude: number;
  routable_points?: RoutablePoint[];
}

interface Address {
  name: string;
  address_number: string;
  street_name: string;
}

interface Context {
  country?: {
    name: string;
    country_code: string;
    country_code_alpha_3: string;
  };
  region?: {
    name: string;
    region_code: string;
    region_code_full: string;
  };
  postcode?: {
    id: string;
    name: string;
  };
  place?: {
    id: string;
    name: string;
  };
  neighborhood?: {
    id: string;
    name: string;
  };
  address?: Address;
  street?: {
    name: string;
  };
}

interface OpenHours {
  periods: {
    open: {
      day: number;
      time: string;
    };
  }[];
  weekday_text: string[];
}

interface Metadata {
  phone?: string;
  website?: string;
  open_hours?: OpenHours;
}

interface SearchboxFeature {
  type: 'Feature';
  geometry: {
    coordinates: [number, number]; // [longitude, latitude]
    type: 'Point';
  };
  properties: {
    name: string;
    mapbox_id: string;
    feature_type: string;
    address?: string;
    full_address?: string;
    place_formatted?: string;
    context: Context;
    coordinates: Coordinates;
    language: string;
    maki?: string;
    poi_category?: string[];
    poi_category_ids?: string[];
    external_ids?: Record<string, string>;
    metadata?: Metadata;
    distance?: number;
  };
}

interface SearchboxResponse {
  type: 'FeatureCollection';
  features: SearchboxFeature[];
  attribution: string;
  response_id: string;
}

export interface SearchResult {
  id: string;
  name: string;
  fullAddress: string;
  placeFormatted: string;
  coordinates: {
    latitude: number;
    longitude: number;
    routablePoints?: {
      name: string;
      latitude: number;
      longitude: number;
    }[];
  };
  details: {
    street?: string;
    streetNumber?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    country?: string;
    countryCode?: string;
    postalCode?: string;
    categories?: string[];
    phone?: string;
    website?: string;
    openingHours?: string[];
    distance?: number;
  };
}

class MapboxService {
  private static formatSearchResult(feature: SearchboxFeature): SearchResult {
    const properties = feature.properties;
    const context = properties.context;

    return {
      id: properties.mapbox_id,
      name: properties.name,
      fullAddress: properties.full_address || properties.address || '',
      placeFormatted: properties.place_formatted || '',
      coordinates: {
        latitude: properties?.coordinates?.latitude,
        longitude: properties?.coordinates?.longitude,
        routablePoints: properties?.coordinates?.routable_points,
      },
      details: {
        street: context?.street?.name,
        streetNumber: context?.address?.address_number,
        neighborhood: context?.neighborhood?.name,
        city: context?.place?.name,
        state: context?.region?.name,
        country: context?.country?.name,
        countryCode: context?.country?.country_code,
        postalCode: context?.postcode?.name,
        categories: properties?.poi_category,
        phone: properties?.metadata?.phone,
        website: properties?.metadata?.website,
        openingHours: properties?.metadata?.open_hours?.weekday_text,
        distance: properties?.distance,
      },
    };
  }

  static async searchPlaces(query: string, options?: SearchboxOptions): Promise<SearchResult[]> {
    try {
      const params = new URLSearchParams({
        q: query,
        access_token: MAPBOX_ACCESS_TOKEN,
      });

      // Add optional parameters
      if (options?.limit) {
        params.append('limit', options.limit.toString());
      }

      if (options?.proximity) {
        params.append('proximity', `${options.proximity.longitude},${options.proximity.latitude}`);
      }

      if (options?.language?.length) {
        params.append('language', options.language.join(','));
      }

      const response = await fetch(`${MAPBOX_SEARCHBOX_API_URL}?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Mapbox Searchbox API request failed');
      }

      const data: SearchboxResponse = await response.json();
      return data.features.map(f => this.formatSearchResult(f));
    } catch (error) {
      console.error('Error searching places:', error);
      throw error;
    }
  }

  /**
   * Get location details from coordinates (reverse geocoding)
   */
  static async reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
    try {
      console.log(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${MAPBOX_ACCESS_TOKEN}&types=address`,
      );
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${MAPBOX_ACCESS_TOKEN}&types=address`,
      );

      if (!response.ok) {
        throw new Error('Mapbox API request failed');
      }

      const data = await response.json();

      console.log('data.features.length', data.features.length);
      if (data.features.length === 0) {
        return null;
      }

      return data.features[0].place_name;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      throw error;
    }
  }

  /**
   * Search for locations with autocomplete
   */
  static async autocomplete(
    query: string,
    proximity?: { latitude: number; longitude: number },
    limit: number = 5,
  ): Promise<SearchResult[]> {
    try {
      const encodedQuery = encodeURIComponent(query);
      let url = `${MAPBOX_SEARCHBOX_API_URL}/${encodedQuery}.json?access_token=${MAPBOX_ACCESS_TOKEN}&limit=${limit}`;

      // Add proximity bias if coordinates are provided
      if (proximity) {
        url += `&proximity=${proximity.longitude},${proximity.latitude}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Mapbox API request failed');
      }

      const data: SearchboxResponse = await response.json();
      return data.features.map(this.formatSearchResult);
    } catch (error) {
      console.error('Error in autocomplete:', error);
      throw error;
    }
  }
}

export default MapboxService;

export const PLACE_TYPE_ICONS: Record<SearchboxCategory, string> = {
  restaurant: 'fork.knife',
  cafe: 'cup.and.saucer',
  bar: 'wineglass',
  hotel: 'bed.double',
  airport: 'airplane',
  hospital: 'cross.case',
  park: 'leaf',
  shopping: 'bag',
  museum: 'building.columns',
  cinema: 'film',
  theater: 'theatermasks',
  gym: 'figure.run',
  pharmacy: 'cross',
  parking: 'p',
  bank: 'banknote',
  school: 'book',
  university: 'graduationcap',
};

// Additional common place types
export const EXTENDED_PLACE_TYPE_ICONS: Record<string, string> = {
  ...PLACE_TYPE_ICONS,
  // Transportation
  bus_station: 'bus',
  train_station: 'tram',
  subway_station: 'train.side.front.car',
  taxi_stand: 'car',

  // Entertainment
  night_club: 'music.note',
  amusement_park: 'sparkles',
  art_gallery: 'paintpalette',

  // Services
  post_office: 'envelope',
  police: 'shield',
  fire_station: 'flame',
  library: 'books.vertical',

  // Shopping
  convenience_store: 'cart',
  supermarket: 'basket',
  mall: 'bag.fill.badge.plus',

  // Food
  bakery: 'birthday.cake',
  ice_cream: 'ice.cream',
  food_court: 'fork.knife',

  // Outdoor
  beach: 'sun.max',
  garden: 'leaf.arrow.circlepath',
  campground: 'tent',

  // Sports
  stadium: 'sportscourt',
  swimming_pool: 'figure.pool.swim',
  sports_centre: 'figure.strengthtraining.traditional',

  // Religious
  place_of_worship: 'building.2',
  church: 'cross',
  mosque: 'moon',

  // Others
  tourist_attraction: 'star',
  viewpoint: 'binoculars',
  historic_site: 'building.columns',
  monument: 'crown',
  embassy: 'flag',
  gas_station: 'fuelpump',
  atm: 'dollarsign',
  veterinary: 'pawprint',
};
