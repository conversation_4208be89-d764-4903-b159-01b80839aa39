import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import * as Crypto from 'expo-crypto';

const IMAGE_CACHE_DIRECTORY = `${FileSystem.cacheDirectory}image-cache/`;

/**
 * Class to manage permanent image caching
 */
class ImageCacheManager {
  constructor() {
    this.ensureCacheDirectoryExists();
  }

  /**
   * Ensure the cache directory exists
   */
  async ensureCacheDirectoryExists() {
    const dirInfo = await FileSystem.getInfoAsync(IMAGE_CACHE_DIRECTORY);
    if (!dirInfo.exists) {
      console.log('Creating cache directory...');
      await FileSystem.makeDirectoryAsync(IMAGE_CACHE_DIRECTORY, { 
        intermediates: true 
      });
    }
  }

  /**
   * Generate a unique file name for caching
   * @param {string} uri - The original image URI
   * @returns {string} A unique filename
   */
  async generateUniqueFileName(uri) {
    // Create a hash from the original URI plus timestamp to ensure uniqueness
    const timestamp = Date.now().toString();
    const data = uri + timestamp;
    const hash = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      data
    );
    return hash.substring(0, 15) + '.jpg';
  }

  /**
   * Compress an image
   * @param {string} uri - The image URI
   * @param {Object} options - Compression options
   * @returns {Promise<Object>} The compressed image info
   */
  async compressImage(uri, { quality = 0.5, width = 1200, height = 1200 } = {}) {
    try {
      const manipulatorOptions = {
        compress: quality,
        format: ImageManipulator.SaveFormat.JPEG,
      };

      // Only add resize if either width or height is provided
      const resize = [];
      if (width || height) {
        resize.push({ 
          width, 
          height, 
          mode: ImageManipulator.ResizeMode.CONTAIN 
        });
      }

      const manipulateResult = await ImageManipulator.manipulateAsync(
        uri,
        resize.length > 0 ? resize : [],
        manipulatorOptions
      );

      return manipulateResult;
    } catch (error) {
      console.error('Error compressing image:', error);
      // If compression fails, return the original URI
      return { uri, width: null, height: null };
    }
  }

  /**
   * Cache an image permanently
   * @param {string} uri - The image URI to cache
   * @returns {Promise<Object>} Information about the cached image
   */
  async cacheImage(uri) {
    try {
      await this.ensureCacheDirectoryExists();

      const fileName = await this.generateUniqueFileName(uri);
      const cachedUri = `${IMAGE_CACHE_DIRECTORY}${fileName}`;

      // Copy the file to our permanent cache location
      await FileSystem.copyAsync({
        from: uri,
        to: cachedUri
      });

      // Create metadata file to store additional info if needed
      const metadataUri = `${cachedUri}.meta`;
      const metadata = {
        originalUri: uri,
        dateCreated: new Date().toISOString(),
      };

      await FileSystem.writeAsStringAsync(
        metadataUri,
        JSON.stringify(metadata),
        { encoding: FileSystem.EncodingType.UTF8 }
      );

      return {
        uri: cachedUri,
        fileName,
        metadataUri,
        metadata
      };
    } catch (error) {
      console.error('Error caching image:', error);
      throw error;
    }
  }

  /**
   * Get all cached images
   * @returns {Promise<Array>} List of cached images
   */
  async getAllCachedImages() {
    try {
      await this.ensureCacheDirectoryExists();
      
      const files = await FileSystem.readDirectoryAsync(IMAGE_CACHE_DIRECTORY);
      
      // Filter out only JPG files (not metadata files)
      const imageFiles = files.filter(file => 
        file.endsWith('.jpg') && !file.endsWith('.meta')
      );
      
      // Create full URIs and get metadata
      const cachedImages = await Promise.all(imageFiles.map(async (fileName) => {
        const uri = `${IMAGE_CACHE_DIRECTORY}${fileName}`;
        const metadataUri = `${uri}.meta`;
        
        try {
          const metadataString = await FileSystem.readAsStringAsync(metadataUri);
          const metadata = JSON.parse(metadataString);
          
          return {
            uri,
            fileName,
            metadata
          };
        } catch (error) {
          // If metadata can't be read, just return the image URI
          return { uri, fileName };
        }
      }));
      
      return cachedImages;
    } catch (error) {
      console.error('Error getting cached images:', error);
      return [];
    }
  }

  /**
   * Delete a cached image
   * @param {string} uri - The cached image URI to delete
   * @returns {Promise<boolean>} Whether deletion was successful
   */
  async deleteCachedImage(uri) {
    try {
      // Delete the image file
      await FileSystem.deleteAsync(uri, { idempotent: true });
      
      // Also delete its metadata file if it exists
      const metadataUri = `${uri}.meta`;
      await FileSystem.deleteAsync(metadataUri, { idempotent: true });
      
      return true;
    } catch (error) {
      console.error('Error deleting cached image:', error);
      return false;
    }
  }

  /**
   * Clean up the cache by removing old images
   * @param {Object} options - Cleanup options
   * @returns {Promise<number>} Number of files deleted
   */
  async cleanupCache({ 
    maxAgeInDays = 30, 
    maxSizeInMB = 100 
  } = {}) {
    try {
      const cachedImages = await this.getAllCachedImages();
      
      // Get total size and find old files
      let totalSizeInBytes = 0;
      const now = new Date();
      const imagesWithInfo = await Promise.all(
        cachedImages.map(async (image) => {
          const fileInfo = await FileSystem.getInfoAsync(image.uri);
          const ageInMs = now - new Date(image.metadata?.dateCreated || 0);
          const ageInDays = ageInMs / (1000 * 60 * 60 * 24);
          
          totalSizeInBytes += fileInfo.size || 0;
          
          return {
            ...image,
            size: fileInfo.size || 0,
            ageInDays
          };
        })
      );
      
      // Sort by age (oldest first)
      imagesWithInfo.sort((a, b) => b.ageInDays - a.ageInDays);
      
      // Delete files that are too old or if we're over the size limit
      const filesToDelete = [];
      let currentSize = totalSizeInBytes;
      const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
      
      for (const image of imagesWithInfo) {
        // Delete if too old
        if (image.ageInDays > maxAgeInDays) {
          filesToDelete.push(image);
          currentSize -= image.size;
          continue;
        }
        
        // Delete oldest files if we're over size limit
        if (currentSize > maxSizeInBytes) {
          filesToDelete.push(image);
          currentSize -= image.size;
        } else {
          // We're under the size limit now
          break;
        }
      }
      
      // Delete the files
      for (const image of filesToDelete) {
        await this.deleteCachedImage(image.uri);
      }
      
      return filesToDelete.length;
    } catch (error) {
      console.error('Error cleaning up cache:', error);
      return 0;
    }
  }
}

// Export a singleton instance
export const imageCacheManager = new ImageCacheManager();
