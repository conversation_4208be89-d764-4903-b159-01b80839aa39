import { create } from 'zustand';

interface SelectionState {
  // State
  selectedEntries: string[];
  
  // Actions
  selectEntry: (entryId: string) => void;
  unselectEntry: (entryId: string) => void;
  toggleEntrySelection: (entryId: string) => void;
  clearSelection: () => void;
  isEntrySelected: (entryId: string) => boolean;
  selectAll: (entryIds: string[]) => void;
  deleteSelected: (deleteCallback: (ids: string[]) => Promise<boolean>) => Promise<void>;
}

export const useSelectionStore = create<SelectionState>((set, get) => ({
  // Initial state
  selectedEntries: [],
  
  // Actions
  selectEntry: (entryId: string) => set(state => ({
    selectedEntries: [...state.selectedEntries, entryId]
  })),
  
  unselectEntry: (entryId: string) => set(state => ({
    selectedEntries: state.selectedEntries.filter(id => id !== entryId)
  })),
  
  toggleEntrySelection: (entryId: string) => {
    const { selectedEntries, isEntrySelected } = get();

    console.log(entryId)
    console.log(selectedEntries)
    
    if (isEntrySelected(entryId)) {
      return get().unselectEntry(entryId);
    } else {
      return get().selectEntry(entryId);
    }
  },
  
  clearSelection: () => set({ selectedEntries: [] }),
  
  isEntrySelected: (entryId: string) => {
    return get().selectedEntries.includes(entryId);
  },
  
  selectAll: (entryIds: string[]) => set({
    selectedEntries: entryIds
  }),
  
  deleteSelected: async (deleteCallback) => {
    const { selectedEntries } = get();
    
    // Process deletions sequentially
    // for (const id of selectedEntries) {
    //   await deleteCallback(id);
    // }

    await deleteCallback(selectedEntries);
    
    // Clear selection after deletion
    get().clearSelection();
  }
}));