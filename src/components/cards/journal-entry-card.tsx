import { BaseText } from '@components/ui/base';
import Pressable from '@components/ui/base/pressable';
import Row from '@components/ui/layout/row';
import CircledIcon from '@components/ui/others/circled-icon';
import { MoodIcon } from '@components/ui/others/mood-icon';
import { cx, formatDate } from '@utils/functions';
import { wp } from '@utils/responsive-dimension';
import { useEffect, useState } from 'react';
import { View } from 'react-native';
import { JournalEntryType, MOOD_STATE, MOOD_VARIANT } from 'src/@types/app-interface';
import ImageCard from './image-card';
import { colors } from '@theme/colors';
import { BookmarkIcon } from '@components/ui/others/icons';
import { JournalEntry } from '@services/db/schema';
import HighlightedText from '@components/ui/others/highlighted-text';
import Animated, {
  LinearTransition,
  SharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import useDelayedProperty from '@hooks/use-delayed-property';
import { TickCircle } from 'iconsax-react-native';

interface JournalEntryCardProps {
  item: JournalEntry;
  showImages?: boolean;
  showMoodIcon?: boolean;
  selected?: boolean;
  searchQuery?: string;
  onPress?: VoidFunction;
  isSelectionActive?: SharedValue<boolean>;
  hasBorder?: boolean;
}

const JournalEntryCard = ({
  item,
  showImages = true,
  onPress,
  showMoodIcon = true,
  searchQuery,
  isSelectionActive,
  selected,
  hasBorder = true,
}: JournalEntryCardProps) => {
  // const [isSelectionActiveValue, setIsSelectionActiveValue] = useState(false);

  const isSelectionActiveValue = useDelayedProperty(isSelectionActive!, true as const, false as const, 0, 0);

  // useEffect(() => {
  //   if (isSelectionActive) {
  //     setIsSelectionActiveValue(isSelectionActive.value);
  //   }
  // }, [isSelectionActive]);

  const hasAddress = Boolean(item?.location?.address);
  const formattedContent = item?.content?.replace(/\n/g, ' ');
  const isSearch = Boolean(searchQuery !== undefined);

  const moodIconStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: withSpring(isSelectionActive?.value ? 0 : 1, { damping: 18, stiffness: 200, velocity: 4 }) },
      ],
    };
  }, []);

  const selectIconStyle = useAnimatedStyle(() => {
    return {
      height: withTiming(isSelectionActive?.value ? 24 : 0, { duration: 200 }),
      width: withTiming(isSelectionActive?.value ? 24 : 0, { duration: 200 }),
      transform: [
        { scale: withSpring(isSelectionActive?.value ? 1 : 0, { damping: 18, stiffness: 200, velocity: 4 }) },
      ],
    };
  }, []);

  const selectedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: withSpring(selected ? 1 : 0, { damping: 28, stiffness: 400, velocity: 20 }) }],
    };
  }, [selected]);

  const leftIElementStyle = useAnimatedStyle(() => {
    return {
      width: withTiming(isSelectionActive?.value ? 24 : showMoodIcon ? 40 : 0, { duration: 200 }),
      // transform: [
      //   { translateX: withSpring(isSelectionActive?.value ? -10 : 0, { damping: 18, stiffness: 200, velocity: 4 }) },
      // ],
    };
  }, [showMoodIcon]);

  return (
    <Pressable activeOpacity={0.9} onPress={onPress}>
      <View className="flex-row items-start">
        <Animated.View style={leftIElementStyle} layout={LinearTransition} className="mr-10">
          {isSelectionActiveValue && (
            <Animated.View
              style={selectIconStyle}
              className="flex-row items-center justify-center border border-dashed border-blue-500 bg-grey-25 rounded-full">
              <Animated.View className="absolute items-center justify-center" style={selectedStyle}>
                <TickCircle variant="Bold" size={wp(24)} color={colors.blue[500]} />
              </Animated.View>
              {/* {selected && <Animated.View className="absolute items-center justify-center ">
                <TickCircle variant="Bold" size={wp(24)} color={colors.blue[500]} />
              </Animated.View>} */}
            </Animated.View>
          )}
          {showMoodIcon && !isSelectionActiveValue && (
            <Animated.View style={moodIconStyle}>
              <MoodIcon mood={item.mood} state={MOOD_STATE.INACTIVE} hasBorder={hasBorder} />
            </Animated.View>
          )}
        </Animated.View>
        <View className={cx('flex-1 mr-12')}>
          {isSearch ? (
            <HighlightedText
              fontSize={16}
              lineHeight={22}
              highlightedColor={colors.blue[500]}
              notHighlightedColor={colors.grey[500]}
              numberOfLines={1}
              highlightedWeight="medium"
              notHighlightedWeight="medium"
              text={createSearchSnippet(item?.title, searchQuery ?? '', 5)}
            />
          ) : (
            <BaseText className="text-grey-500" weight="medium" fontSize={16} numberOfLines={1} lineHeight={22}>
              {item?.title}
            </BaseText>
          )}
          {isSearch ? (
            <HighlightedText
              fontSize={14}
              lineHeight={22}
              highlightedColor={colors.blue[500]}
              notHighlightedColor={colors.grey[300]}
              numberOfLines={1}
              highlightedWeight="medium"
              notHighlightedWeight="regular"
              text={createSearchSnippet(formattedContent, searchQuery ?? '', 7)}
            />
          ) : (
            <BaseText numberOfLines={1} fontSize={14} lineHeight={22} className="text-grey-300 mt-4">
              {formattedContent}
            </BaseText>
          )}
          {!isSearch && (
            <Row disableSpread style={{ gap: wp(5) }} className="flex-1 mt-4">
              {hasAddress && (
                <>
                  <View className="flex-1">
                    <BaseText numberOfLines={1} fontSize={14} lineHeight={22} className="text-grey-300">
                      {item?.location?.address}
                    </BaseText>
                  </View>
                  <View className="h-5 w-5 rounded-full bg-[#D9D9D9]" />
                </>
              )}
              <BaseText numberOfLines={1} fontSize={14} lineHeight={22} className="text-grey-300">
                {formatDate(item?.entry_date, 'hh:mm A')}
              </BaseText>
              <View className="h-5 w-5 rounded-full bg-[#D9D9D9]" />
              <Pressable>
                <BookmarkIcon size={wp(18)} color={item?.bookmarked ? colors.purple[500] : colors.grey[300]} />
              </Pressable>
            </Row>
          )}
          {showImages && item?.images.length > 0 && (
            <View className="mt-20">
              <ImageCard images={item?.images} />
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
};

export default JournalEntryCard;

/**
 * Highlights search terms in text by adding asterisks around matching words
 * @param text The full text to search within
 * @param searchQuery The search term to highlight
 * @returns Text with highlighted search terms
 */
function highlightSearchTerms(text: string, searchQuery: string): string {
  if (!text || !searchQuery || searchQuery.trim() === '') {
    return text;
  }

  // Escape special regex characters in the search query
  const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  // Create a case-insensitive regex to match the search term
  const searchRegex = new RegExp(`(${escapedQuery})`, 'gi');

  // Replace matches with the same text surrounded by asterisks
  return text.replace(searchRegex, '*$1*');
}

/**
 * Creates a text snippet centered around the search match
 * @param text The full text to search within
 * @param searchQuery The search term to find
 * @param contextWords Number of words to include before and after the match
 * @returns A snippet of text with highlighted search term
 */
function createSearchSnippet(text: string, searchQuery: string, contextWords: number = 5): string {
  if (!text || !searchQuery || searchQuery.trim() === '') {
    // Return a default snippet from the beginning if no search query
    return (
      text
        .split(' ')
        .slice(0, contextWords * 2)
        .join(' ') + '...'
    );
  }

  // Escape special regex characters in the search query
  const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  // Create a case-insensitive regex to find the search term
  const searchRegex = new RegExp(escapedQuery, 'i');

  // Find the position of the match
  const matchIndex = text.search(searchRegex);

  if (matchIndex === -1) {
    // No match found, return beginning of text
    return (
      text
        .split(' ')
        .slice(0, contextWords * 2)
        .join(' ') + '...'
    );
  }

  // Split the text into words
  const words = text.split(' ');

  // Find which word contains the match
  let currentPosition = 0;
  let matchWordIndex = 0;

  for (let i = 0; i < words.length; i++) {
    currentPosition += words[i].length + 1; // +1 for the space
    if (currentPosition > matchIndex) {
      matchWordIndex = i;
      break;
    }
  }

  // Calculate start and end indices for the snippet
  const startIndex = Math.max(0, matchWordIndex - contextWords);
  const endIndex = Math.min(words.length, matchWordIndex + contextWords + 1);

  // Create the snippet
  let snippet = '';

  // Add ellipsis at beginning if we're not starting from the first word
  if (startIndex > 0) {
    snippet += '... ';
  }

  // Add the context words and the word with the match
  snippet += words.slice(startIndex, endIndex).join(' ');

  // Add ellipsis at end if we're not ending at the last word
  if (endIndex < words.length) {
    snippet += ' ...';
  }

  // Highlight the search term in the snippet
  return highlightSearchTerms(snippet, searchQuery);
}
