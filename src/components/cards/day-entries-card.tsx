import JournalEntryCard from '@components/cards/journal-entry-card';
import { BaseText } from '@components/ui/base';
import { hp, wp } from '@utils/responsive-dimension';
import { View } from 'react-native';
import { JournalEntryType, MOOD_VARIANT } from 'src/@types/app-interface';
import ImageCard, { MiniImageCard } from './image-card';
import Row from '@components/ui/layout/row';
import { MoodIcon } from '@components/ui/others/mood-icon';
import { cx } from '@utils/functions';
import { JournalEntry } from '@services/db/schema';
import dayjs from 'dayjs';
import { useNavigation } from '@react-navigation/native';
import Animated, { SharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import useDelayedProperty from '@hooks/use-delayed-property';
import { useSelectionStore } from '@store/useSelectionStore';

interface DayEntriesCardProps {
  entries: {
    date: Date;
    entries: JournalEntry[];
  };
  isSelectionActive?: SharedValue<boolean>;
  handleSelectEntry: (entry: any) => void;
}

const DayEntriesCard = ({ entries, isSelectionActive, handleSelectEntry }: DayEntriesCardProps) => {
  const { isEntrySelected } = useSelectionStore();
  
  const isSelectionActiveValue = useDelayedProperty(isSelectionActive!, true as const, false as const, 0, 0);

  const navigation = useNavigation();

  const moods = Array.from(new Set(entries?.entries?.map(e => e.mood)));
  const hasMoods = moods?.length > 0;

  const images = entries?.entries?.map(e => e.images).flat();
  const hasImages = images?.length > 0;

  const dateWithImageContainerStyle = useAnimatedStyle(() => {
    return {
      marginLeft: withSpring(isSelectionActive?.value ? -60 : 0, { damping: 18, stiffness: 200, velocity: 4 }),
      transform: [
        { translateX: withSpring(isSelectionActive?.value ? -10 : 0, { damping: 18, stiffness: 200, velocity: 4 }) },
      ],
    };
  }, []);

  const dateTitleContainerStyle = useAnimatedStyle(() => {
    return {
      marginTop: withSpring(isSelectionActive?.value ? 0 : -12, { damping: 18, stiffness: 200, velocity: 4 }),
      marginLeft: 13,
      marginBottom: withSpring(isSelectionActive?.value ? 14 : 0, { damping: 18, stiffness: 200, velocity: 4 }),
      transform: [
        { translateY: withSpring(isSelectionActive?.value ? 0 : -50, { damping: 18, stiffness: 200, velocity: 4 }) },
      ],
    };
  }, []);

  return (
    <View className="mt-8 bg-grey-25 p-8 rounded-16 z-1 overflow-hidden">
      <Animated.View style={dateTitleContainerStyle}>
        <BaseText fontSize={14} weight="medium" className=" text-grey-300">
          {dayjs(entries.date).format('dddd').toUpperCase()} -{' '}
          <BaseText fontSize={14} weight="medium" className=" text-grey-500">
            {dayjs(entries.date).format('DD').toUpperCase()}
          </BaseText>
        </BaseText>
      </Animated.View>
      <View className="flex-row" style={{ gap: hp(8) }}>
        <Animated.View className="items-center" style={dateWithImageContainerStyle}>
          <View className="bg-white rounded-10 p-8 w-[56px] h-[56px] items-center justify-center">
            <BaseText fontSize={16} className="text-center text-grey-500">
              {dayjs(entries.date).format('ddd')}
            </BaseText>
            <BaseText fontSize={20} lineHeight={22} weight="bold" className="text-center text-grey-500 mt-3">
              {dayjs(entries.date).format('DD').toUpperCase()}
            </BaseText>
          </View>
          {hasImages && (
            <View className="-mt-12 z-2">
              <MiniImageCard images={images} />
            </View>
          )}
        </Animated.View>
        <View className="flex-1" style={{ gap: hp(14) }}>
          {entries?.entries?.map((i, idx) => (
            <JournalEntryCard
              key={idx}
              item={i}
              showImages={false}
              showMoodIcon={false}
              isSelectionActive={isSelectionActive}
              selected={isEntrySelected(i.id)}
              onPress={() => {
                if (isSelectionActiveValue) {
                  handleSelectEntry(i.id);
                  return;
                }

                navigation.navigate('WriteJournalScreen', {
                  journalId: i.id,
                  images: i.images,
                  title: i.title,
                  entry_date: i.entry_date,
                });
              }}
            />
          ))}
          {hasMoods && (
            <Row disableSpread className="mt-4">
              {moods.map((i, idx) => (
                <View key={idx} className={cx({ '-ml-12': idx !== 0 })}>
                  <MoodIcon mood={i} size={wp(16)} />
                </View>
              ))}
            </Row>
          )}
        </View>
      </View>
    </View>
  );
};

export default DayEntriesCard;
