import JournalEntryCard from '@components/cards/journal-entry-card';
import { BaseText } from '@components/ui/base';
import { hp, wp } from '@utils/responsive-dimension';
import { View } from 'react-native';
import { JournalEntryType, MOOD_VARIANT } from 'src/@types/app-interface';
import ImageCard, { MiniImageCard } from './image-card';
import Row from '@components/ui/layout/row';
import { MoodIcon } from '@components/ui/others/mood-icon';
import { cx } from '@utils/functions';
import { JournalEntry } from '@services/db/schema';
import dayjs from 'dayjs';
import { useSharedValue } from 'react-native-reanimated';

interface SearchEntryResultCardProps {
  entry: JournalEntry;
  searchQuery?: string;
}

const SearchEntryResultCard = ({ entry, searchQuery }: SearchEntryResultCardProps) => {
  const isSelectionActive = useSharedValue(false);

  return (
    <View className="bg-grey-25 p-12 rounded-16 z-1" style={{ gap: hp(8) }}>
      {!searchQuery && (
        <Row className="">
          <BaseText fontSize={14} className=" text-grey-500">
            {dayjs(entry.entry_date).format('Do MMMM YYYY')}
          </BaseText>
          <BaseText fontSize={14} className=" text-grey-500">
            {dayjs(entry.entry_date).format('dddd')}
          </BaseText>
        </Row>
      )}
      <JournalEntryCard
        item={entry}
        showImages={false}
        showMoodIcon={false}
        searchQuery={searchQuery}
        isSelectionActive={isSelectionActive}
      />
      {searchQuery && (
        <BaseText fontSize={14} className=" text-grey-300">
          {dayjs(entry.entry_date).format('dddd, DD MMMM YYYY')}
        </BaseText>
      )}
    </View>
  );
};

export default SearchEntryResultCard;
