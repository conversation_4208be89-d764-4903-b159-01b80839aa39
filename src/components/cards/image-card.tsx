import { BaseText } from '@components/ui/base';
import Pressable from '@components/ui/base/pressable';
import CustomImage from '@components/ui/others/custom-image';
import { cx } from '@utils/functions';
import { wp } from '@utils/responsive-dimension';
import { BlurView } from 'expo-blur';
import { View } from 'react-native';
import Animated, { interpolate, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

interface ImageCardProps {
  images: string[];
}

const DURATION = 40;

const ImageCard = ({ images }: ImageCardProps) => {
  const imagesToShow = images.slice(0, 3);
  const showMoreLabel = images.length > 3;

  return (
    <View className="flex-row items-start justify-start">
      {imagesToShow.map((i, idx) => (
        <ImageView
          key={idx}
          image={i}
          imagePosition={imagesToShow.length as keyof typeof imageStyling}
          activeIndex={idx}
        />
      ))}
      {showMoreLabel && (
        <BlurView
          tint="light"
          intensity={50}
          className="absolute py-6 px-12 rounded-full overflow-hidden top-40 left-[18%]">
          <BaseText fontSize={14} lineHeight={22} className="text-white">
            +{images.length - 3} more Photos
          </BaseText>
        </BlurView>
      )}
    </View>
  );
};

export default ImageCard;

export const MiniImageCard = ({ images, showLabels = true }: ImageCardProps & { showLabels?: boolean }) => {
  const imagesToShow = images.slice(0, 2);
  const showMoreLabel = images.length > 2 && showLabels;

  return (
    <View className="flex-row items-start justify-start">
      {imagesToShow.map((i, idx) => (
        <ImageView
          key={idx}
          image={i}
          isMini
          imagePosition={imagesToShow.length as keyof typeof imageStyling}
          activeIndex={idx}
        />
      ))}
      {showMoreLabel && (
        <View
          className="absolute overflow-hidden top-3 -left-[15px]"
          style={{ transform: imageStyling[2][1].transform }}>
          <BaseText fontSize={14} lineHeight={22} className="text-white">
            +{images.length - 3}
          </BaseText>
        </View>
      )}
    </View>
  );
};

const imageStyling = {
  [1]: [{ transform: [{ rotate: '4.23deg' }], zIndex: 0 }],
  [2]: [
    { transform: [{ rotate: '-6.67deg' }], zIndex: 0 },
    { transform: [{ rotate: '10.47deg' }], zIndex: -1, marginLeft: -wp(50) },
  ],
  [3]: [
    { transform: [{ rotate: '4.27deg' }], zIndex: 0 },
    { transform: [{ rotate: '-9.59deg' }], zIndex: -1, marginLeft: -wp(40) },
    { transform: [{ rotate: '13.24deg' }], zIndex: -2, marginLeft: -wp(50) },
  ],
};

const ImageView = ({
  image,
  imagePosition,
  activeIndex,
  isMini = false,
}: {
  image: string;
  imagePosition: keyof typeof imageStyling;
  activeIndex: number;
  isMini?: boolean;
}) => {
  const transition = useSharedValue(0);
  const isActive = useSharedValue(true);

  const style = useAnimatedStyle(() => ({
    transform: [{ scale: interpolate(transition.value, [0, 1], [1, 0.95]) }],
  }));
  const dynamicStyle = imageStyling[imagePosition][activeIndex];

  return (
    <Pressable
      className="self-start"
      style={dynamicStyle}
      activeOpacity={1}
      disabled={isMini}
      onPressIn={() => {
        isActive.value = true;
        transition.value = withTiming(1, { duration: DURATION }, () => {
          if (!isActive.value) {
            transition.value = withTiming(0, { duration: DURATION });
          }
        });
      }}
      onPressOut={() => {
        if (transition.value === 1) {
          transition.value = withTiming(0, { duration: DURATION });
        }
        isActive.value = false;
      }}>
      <ImageContainer>
        <Animated.View className={cx('bg-white rounded-8 p-3', { 'p-3': !isMini, 'p-2': isMini })} style={[style]}>
          <CustomImage
            imageProps={{ source: { uri: image }, contentFit: 'cover' }}
            className={cx('rounded-[6px]', { 'h-24 w-24': isMini, 'w-100 h-120 ': !isMini })}
          />
        </Animated.View>
      </ImageContainer>
    </Pressable>
  );
};

const ImageContainer = ({ children }: { children: JSX.Element }) => {
  return (
    <View
      style={{
        shadowColor: '#00000093',
        shadowOffset: { width: 0, height: 9 },
        shadowOpacity: 0.21,
        shadowRadius: 10.5,
      }}>
      <View
        style={{
          shadowColor: '#00000093',
          shadowOffset: { width: -3.6, height: 3.6 },
          shadowOpacity: 0.08,
          shadowRadius: 5.5,
        }}>
        <View
          style={{
            shadowColor: '#00000093',
            shadowOffset: { width: -1.82, height: 1.82 },
            shadowOpacity: 0.1,
            shadowRadius: 1.5,
          }}>
          <View
            style={{
              shadowColor: '#00000093',
              shadowOffset: { width: -0.91, height: 0.91 },
              shadowOpacity: 0.12,
              shadowRadius: 0.005,
            }}>
            {children}
          </View>
        </View>
      </View>
    </View>
  );
};
