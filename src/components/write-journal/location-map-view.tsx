import { View, ActivityIndicator } from 'react-native';
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { MarkerPinIcon } from '@components/ui/others/icons';
import { BaseText } from '@components/ui/base';
import Pressable from '@components/ui/base/pressable';
import { colors } from '@theme/colors';
import { wp } from '@utils/responsive-dimension';
import { useRef } from 'react';

interface LocationMapViewProps {
  currentLocation: {
    latitude: number;
    longitude: number;
    address?: string;
  } | null;
  isLoading?: boolean;
  error: string | null;
  onRetry: () => void;
  mapViewRef?: React.RefObject<MapView>;
}

const LocationMapView = ({ currentLocation, isLoading, error, onRetry, mapViewRef }: LocationMapViewProps) => {
  return (
    <View className="w-full h-[268px] mt-20 rounded-20 overflow-hidden">
      {isLoading ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color={colors.grey[500]} />
        </View>
      ) : error ? (
        <View className="flex-1 justify-center items-center">
          <BaseText fontSize={16} className="text-grey-500 text-center">
            {error}
          </BaseText>
          <Pressable className="mt-12 bg-grey-100 px-16 py-8 rounded-full" onPress={onRetry}>
            <BaseText fontSize={14} className="text-grey-600">
              Retry
            </BaseText>
          </Pressable>
        </View>
      ) : currentLocation ? (
        <MapView
          ref={mapViewRef}
          style={{ width: '100%', height: '100%' }}
          rotateEnabled={true}
          scrollEnabled={true}
          zoomEnabled={true}
          pitchEnabled={true}
          toolbarEnabled={false}
          initialRegion={{
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          }}>
          <Marker
            coordinate={{
              latitude: currentLocation.latitude,
              longitude: currentLocation.longitude,
            }}>
            <MarkerPinIcon size={wp(24)} color={colors.red[500]} />
          </Marker>
        </MapView>
      ) : null}
    </View>
  );
};

export default LocationMapView;
