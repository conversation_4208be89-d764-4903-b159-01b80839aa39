import { colors } from '@theme/colors';
import { wp } from '@utils/responsive-dimension';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Keyboard, KeyboardAvoidingView, View } from 'react-native';
import Row from '@components/ui/layout/row';
import CircledIcon from '@components/ui/others/circled-icon';
import { BookmarkIcon, CameraIcon, ImageIcon, MarkerPinIcon, RecordingIcon } from '@components/ui/others/icons';
import Pressable, { PressableProps } from '@components/ui/base/pressable';
import { Canvas, Group, Circle, vec, BlurMask } from '@shopify/react-native-skia';
import { useEffect, useMemo, useState } from 'react';
import Animated, {
  SharedValue,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withRepeat,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useImagePicker } from '@hooks/use-image-picker';
import LocationDetailsModal from './location-details-modal';
import { LocationSearchResult } from '@hooks/use-location-search';
import { cx } from '@utils/functions';
import { ArrowCircleDown2, ArrowDown2, TickCircle } from 'iconsax-react-native';
import useKeyboard from '@hooks/use-keyboard';
import { BlurView } from 'expo-blur';
import CustomImage from '@components/ui/others/custom-image';
import { BaseText } from '@components/ui/base';

interface BottomToolboxProps {
  selectedLocationDetail: Omit<LocationSearchResult, 'details'> | null;
  onLocationSelect: (location: LocationSearchResult) => void;
  isLocationModalVisible: boolean;
  setIsLocationModalVisible: (visible: boolean) => void;
  onImageSelect: (images: string[]) => void;
  onCameraCapture: (image: string) => void;
  isBookmarked: boolean;
  isEditValue?: SharedValue<boolean>;
  onBookmarkToggle: () => void;
}

const BottomToolbox = ({
  selectedLocationDetail,
  onLocationSelect,
  isLocationModalVisible,
  setIsLocationModalVisible,
  onImageSelect,
  onCameraCapture,
  isBookmarked,
  isEditValue,
  onBookmarkToggle,
}: BottomToolboxProps) => {
  const { bottom } = useSafeAreaInsets();
  const { pickImage, launchCamera } = useImagePicker();

  const isKeyboardActive = useKeyboard();

  const showToolBox = useSharedValue(0);

  useEffect(() => {
    setTimeout(
      () => {
        showToolBox.value = 1;
      },
      isEditValue?.value ? 2500 : 500,
    );
  }, [showToolBox, isEditValue]);

  const containerStyle = useAnimatedStyle(() => ({
    // opacity: showToolBox.value === 1 ? 1 : 0,
    transform: [
      {
        translateY: showToolBox.value === 1 ? withSpring(0, { damping: 18, stiffness: 250, velocity: 45 }) : 100,
      },
      { scale: showToolBox.value === 1 ? withTiming(1, { duration: 700 }) : 0.95 },
    ],
  }));

  const handleImagePick = async () => {
    const result = await pickImage();
    if (!result.canceled) {
      onImageSelect(result.assets.map(asset => asset.uri));
    }
  };

  const handleCameraLaunch = async () => {
    const result = await launchCamera();
    if (!result.canceled) {
      onCameraCapture(result.assets[0].uri);
    }
  };

  const actions = [
    {
      icon: (
        <CircledIcon className="bg-grey-50 p-12">
          <CameraIcon size={wp(22)} color={colors.grey[300]} />
        </CircledIcon>
      ),
      onPress: handleCameraLaunch,
    },
    {
      icon: (
        <CircledIcon className="bg-grey-50 p-12">
          <ImageIcon size={wp(22)} color={colors.grey[300]} />
        </CircledIcon>
      ),
      onPress: handleImagePick,
    },
    {
      icon: (
        <CircledIcon
          className={cx('p-12', { 'bg-grey-50': !selectedLocationDetail, 'bg-blue-50': selectedLocationDetail })}>
          <MarkerPinIcon size={wp(22)} color={selectedLocationDetail ? colors.blue[500] : colors.grey[300]} />
        </CircledIcon>
      ),
      onPress: () => setIsLocationModalVisible(true),
    },
    {
      icon: (
        <CircledIcon className={cx('p-12', { 'bg-grey-50': !isBookmarked, 'bg-purple-50': isBookmarked })}>
          <BookmarkIcon size={wp(22)} color={isBookmarked ? colors.purple[500] : colors.grey[300]} />
        </CircledIcon>
      ),
      onPress: onBookmarkToggle,
    },
  ];

  return (
    <KeyboardAvoidingView
      className="absolute bottom-0 w-full"
      // keyboardVerticalOffset={bottom}
      behavior="position">
      <Animated.View style={containerStyle}>
        <View className="absolute bottom-0 right-0 left-0 top-0 w-[500px] h-full">
          <CustomImage
            imageProps={{
              source: require('@assets/images/blur-bg.png'),
              contentFit: 'cover',
            }}
            className="w-full h-full"
          />
        </View>
        <Row style={{ paddingBottom: bottom > 0 ? bottom : 20 }} className={`pb-16 px-24`}>
          <Row disableSpread style={{ gap: wp(10) }}>
            {actions.map((i, idx) => (
              <Pressable key={idx} onPress={i.onPress}>
                {i.icon}
              </Pressable>
            ))}
          </Row>
          {isKeyboardActive && (
            <Pressable
              className="flex-row p-10 px-12 rounded-full items-center justify-center bg-grey-50"
              onPress={() => Keyboard.dismiss()}>
              <TickCircle variant="Bold" size={wp(18)} color={colors.grey[300]} />
              <BaseText fontSize={16} weight="medium" className="text-grey-300 ml-5">
                Done
              </BaseText>
              {/* <ArrowCircleDown2 variant="Bold" size={wp(22)} color={colors.grey[300]} /> */}
            </Pressable>
          )}
          {/* {!isKeyboardActive && <RecordAudioButton />} */}
        </Row>
        <LocationDetailsModal
          visible={isLocationModalVisible}
          closeModal={() => setIsLocationModalVisible(false)}
          onLocationSelect={onLocationSelect}
          selectedLocationDetail={selectedLocationDetail}
        />
      </Animated.View>
    </KeyboardAvoidingView>
  );
};

export default BottomToolbox;

const RecordAudioButton = ({ ...props }: PressableProps) => {
  const [baseWidth, setBaseWidth] = useState(0);

  const canvasSize = useMemo(() => baseWidth * 2.5, [baseWidth]);
  const circleSize = baseWidth * 0.4;

  // const centerX = canvasSize / 2;
  // const centerY = canvasSize / 2;

  const offsetX = canvasSize / 2 - circleSize * 2.3;
  const offsetY = canvasSize / 2 - circleSize * 2.25;

  const colorProgress = useSharedValue(0);

  // Start animation when component mounts
  useEffect(() => {
    colorProgress.value = withRepeat(
      withTiming(1, { duration: 1000 }),
      -1, // -1 for infinite repeat
      true, // reverse animation
    );
  }, [colorProgress]);

  // Animated colors
  const color1 = useDerivedValue(() => {
    // Animate between blue and purple
    return `rgba(${59 + 128 * colorProgress.value}, ${171 - 50 * colorProgress.value}, ${
      250 - 50 * colorProgress.value
    }, 0.9)`;
  });

  const color2 = useDerivedValue(() => {
    // Animate between blue and orange
    return `rgba(${59 + 196 * colorProgress.value}, ${171 - 80 * colorProgress.value}, ${
      250 - 180 * colorProgress.value
    }, 0.9)`;
  });

  const color3 = useDerivedValue(() => {
    // Animate between purple and blue
    return `rgba(${187 - 128 * colorProgress.value}, ${107 + 64 * colorProgress.value}, ${
      217 + 33 * colorProgress.value
    }, 0.9)`;
  });

  const color4 = useDerivedValue(() => {
    // Animate between orange and purple
    return `rgba(${255 - 67 * colorProgress.value}, ${139 - 32 * colorProgress.value}, ${
      2 + 215 * colorProgress.value
    }, 0.9)`;
  });

  return (
    <Pressable
      {...props}
      className="flex-row justify-center items-center bg-grey-50 p-12 rounded-full"
      onLayout={e => setBaseWidth(e.nativeEvent.layout.width)}>
      <Canvas
        style={{
          flex: 1,
          height: canvasSize,
          width: canvasSize,
          position: 'absolute',
          overflow: 'visible',
          left: -((canvasSize - baseWidth) / 2),
          top: -((canvasSize - baseWidth) / 2),
        }}>
        <Group opacity={0.9} transform={[{ translateX: offsetX }, { translateY: offsetY }]}>
          <Circle c={vec(circleSize * 3, circleSize * 1.5)} r={circleSize} color={color1} />
          <Circle c={vec(circleSize * 3, circleSize * 3)} r={circleSize} color={color2} />
          <Circle c={vec(circleSize * 1.6, circleSize * 2)} r={circleSize} color={color3} />
          <Circle c={vec(circleSize * 2, circleSize * 3)} r={circleSize} color={color4} />
        </Group>
        <BlurMask blur={15} style="normal" />
      </Canvas>
      <RecordingIcon size={wp(22)} color={colors.white} />
    </Pressable>
  );
};
