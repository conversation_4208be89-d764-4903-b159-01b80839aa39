import { View, Alert, Text, ActivityIndicator } from 'react-native';
import { wp } from '@utils/responsive-dimension';
import SheetModal, { SheetModalProps } from '@components/ui/modals/sheet-modal';
import { BaseText } from '@components/ui/base';
import { LocationData, useLocation } from '@hooks/use-location';
import { colors } from '@theme/colors';
import Pressable from '@components/ui/base/pressable';
import { CancelIcon, ChevronRightIcon } from '@components/ui/others/icons';
import Row from '@components/ui/layout/row';
import LocationMapView from './location-map-view';
import { ScrollView, TextInput } from 'react-native-gesture-handler';
import { LocationSearchResult, useLocationSearch } from '@hooks/use-location-search';
import { SymbolView } from 'expo-symbols';
import { useRef, useState } from 'react';
import MapView from 'react-native-maps';
import PlaceIcon from '@components/ui/others/place-icon';
import AvoidKeyboard from '@components/ui/layout/avoid-keyboard';

interface LocationDetailsModalProps extends Partial<SheetModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  onLocationSelect?: (location: LocationSearchResult) => void;
  selectedLocationDetail?: Omit<LocationSearchResult, 'details'> | null;
}

const LocationDetailsModal = ({
  closeModal,
  onLocationSelect,
  selectedLocationDetail,
  ...props
}: LocationDetailsModalProps) => {
  const [searchResults, setSearchResults] = useState<LocationSearchResult[]>([]);
  const { currentLocation, getCurrentLocation } = useLocation();
  const { searchPlaces, reverseGeocode, isSearching } = useLocationSearch(2000);
  const mapViewRef = useRef<MapView>(null);

  const handleSearch = (searchText: string) => {
    searchPlaces(searchText, {
      // proximity: currentLocation,
      limit: 3,
      language: ['en'],
      onSuccess: results => {
        if (results.length > 0) {
          setSearchResults(results);
          // setCurrentLocation(results[0]);
        }
      },
      onError: () => {
        Alert.alert('Error', 'Failed to search location');
      },
    });
  };

  const handleLocationSelect = (location: LocationSearchResult) => {
    onLocationSelect?.(location);
    mapViewRef.current?.animateCamera(
      {
        center: {
          latitude: location.latitude,
          longitude: location.longitude,
        },
        pitch: 45,
        heading: 0,
        altitude: 1000,
        zoom: 15,
      },
      { duration: 2000 },
    );
    setSearchResults([]);
    // closeModal();
  };

  const handleSelectCurrentLocation = async () => {
    try {
      await getCurrentLocation();
      const reverseResponse = await reverseGeocode(currentLocation?.latitude!, currentLocation?.longitude!);
      if (reverseResponse) {
        console.log(
          JSON.stringify({
            latitude: currentLocation?.latitude,
            longitude: currentLocation?.longitude,
            address: reverseResponse,
            name: reverseResponse,
            details: {},
          }),
          onLocationSelect?.({
            latitude: currentLocation?.latitude!,
            longitude: currentLocation?.longitude!,
            address: reverseResponse,
            name: reverseResponse,
            details: {},
          }),
        );
        mapViewRef.current?.animateCamera(
          {
            center: {
              latitude: currentLocation?.latitude!,
              longitude: currentLocation?.longitude!,
            },
            pitch: 45,
            heading: 0,
            altitude: 1000,
            zoom: 15,
          },
          { duration: 2000 },
        );
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }
  };

  return (
    <SheetModal {...props} closeModal={closeModal}>
      <Row className="px-20">
        <BaseText fontSize={24} weight="medium" className="mb-8">
          Add location
        </BaseText>
        <Pressable onPress={closeModal} className="rounded-full items-center justify-center bg-grey-50 py-6 px-12">
          <CancelIcon size={wp(18)} color={colors.grey[800]} />
        </Pressable>
      </Row>
      <AvoidKeyboard>
        <ScrollView>
          <View className="pb-100 px-20">
            <LocationMapView
              mapViewRef={mapViewRef}
              currentLocation={selectedLocationDetail ?? currentLocation}
              error={null}
              onRetry={() => {}}
            />
            <View className="bg-white p-12 pb-0 mt-10 rounded-10">
              <View className="flex-row bg-[#7878801c] p-12 rounded-12">
                <TextInput
                  className="flex-1"
                  placeholderTextColor={'#3c3c43b9'}
                  placeholder="Search or Enter an Address "
                  onChangeText={handleSearch}
                  style={{
                    fontSize: wp(16),
                    fontFamily: 'circularBook',
                  }}
                />
                {isSearching && <ActivityIndicator size="small" color={colors.grey[300]} />}
              </View>
              <Row>
                <View className="bg-[#F4F4F4] p-3 rounded-full">
                  <View className="p-8 border-2 border-dashed border-grey-300 rounded-full">
                    <SymbolView
                      name="mappin.and.ellipse"
                      style={{
                        width: wp(18),
                        height: wp(18),
                      }}
                      type="monochrome"
                      tintColor={colors.grey[700]}
                    />
                  </View>
                </View>
                <View className="flex-1 ml-16 border-b py-10 border-b-grey-50">
                  <BaseText fontSize={16} lineHeight={22} className="text-grey-700">
                    Use Location Pin
                  </BaseText>
                  <BaseText fontSize={14} lineHeight={20} className="text-grey-300">
                    Stanford University
                  </BaseText>
                </View>
                <ChevronRightIcon size={wp(30)} color={colors.grey[300]} />
              </Row>
              <Row>
                <View className="bg-[#F4F4F4] p-3 rounded-full">
                  <View className="p-8 border-2 border-dashed border-grey-300 rounded-full">
                    <SymbolView
                      name="location.fill"
                      style={{
                        width: wp(18),
                        height: wp(18),
                      }}
                      type="monochrome"
                      tintColor={colors.grey[700]}
                    />
                  </View>
                </View>
                <Pressable className="flex-1 ml-16 py-10" onPress={handleSelectCurrentLocation}>
                  <BaseText fontSize={16} lineHeight={22} className="text-grey-700">
                    Use Current Location
                  </BaseText>
                  <BaseText fontSize={14} lineHeight={20} className="text-grey-300">
                    Fabrick Square
                  </BaseText>
                </Pressable>
                <ChevronRightIcon size={wp(30)} color={colors.grey[300]} />
              </Row>
            </View>
            {searchResults.length > 0 && (
              <View className="mt-10">
                <BaseText fontSize={16} weight="medium" className="text-grey-300">
                  Search Result
                </BaseText>
                <View className="bg-white p-12 pb-0 mt-10 rounded-10">
                  {searchResults.map((result, idx) => (
                    <Pressable key={idx} onPress={() => handleLocationSelect?.(result)}>
                      <LocationOptionCard
                        placeType={result.details.categories?.[0] || 'building'}
                        title={result?.address || result.name}
                        subTitle={result.name}
                      />
                    </Pressable>
                  ))}
                </View>
              </View>
            )}
          </View>
        </ScrollView>
      </AvoidKeyboard>
    </SheetModal>
  );
};

export default LocationDetailsModal;

const LocationOptionCard = ({ placeType, title, subTitle }: { placeType: string; title: string; subTitle: string }) => {
  return (
    <Row>
      <View className="bg-[#F4F4F4] p-3 rounded-full">
        <View className="p-8 border-2 border-dashed border-grey-300 rounded-full">
          <PlaceIcon placeType={placeType} size={wp(18)} color={colors.grey[700]} />
        </View>
      </View>
      <View className="flex-1 ml-16 border-b py-10 border-b-grey-50">
        <BaseText fontSize={16} lineHeight={22} className="text-grey-700" numberOfLines={1}>
          {subTitle}
        </BaseText>
        <BaseText fontSize={14} lineHeight={20} className="text-grey-300" numberOfLines={1}>
          {title}
        </BaseText>
      </View>
      <ChevronRightIcon size={wp(30)} color={colors.grey[300]} />
    </Row>
  );
};
