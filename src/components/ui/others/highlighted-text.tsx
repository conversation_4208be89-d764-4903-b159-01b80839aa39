import React from 'react';
import { Text, View } from 'react-native';
import { BaseText } from '../base';
import { cx } from '@utils/functions';
import { colors } from '@theme/colors';
import { BaseTextProps } from '../base/base-text';

type HighlightedTextProps = {
  text: string;
  fontSize?: number;
  lineHeight?: number;
  className?: string;
  highlightedColor?: string;
  highlightedWeight?: BaseTextProps['weight'];
  notHighlightedWeight?: BaseTextProps['weight'];
  notHighlightedColor?: string;
} & Partial<BaseTextProps>;

const HighlightedText: React.FC<HighlightedTextProps> = ({
  text,
  fontSize = 32,
  lineHeight = 35,
  className,
  highlightedColor = colors.grey[300],
  notHighlightedColor = colors.grey[600],
  highlightedWeight = 'medium',
  notHighlightedWeight = 'medium',
  ...rest
}) => {
  // Process text to find highlighted sections while preserving word boundaries
  const processTextForRendering = () => {
    // First, split by spaces to get individual words/word groups
    const spaceSeparatedParts = text.split(' ');
    
    // Process each space-separated part to find highlights
    return spaceSeparatedParts.map(part => {
      // Check if this part contains any highlighting
      if (part.includes('*')) {
        // Split the part by asterisks
        const segments = [];
        let inHighlight = false;
        let currentText = '';
        
        // Process each character
        for (let i = 0; i < part.length; i++) {
          if (part[i] === '*') {
            if (currentText) {
              segments.push({
                text: currentText,
                highlighted: inHighlight
              });
              currentText = '';
            }
            inHighlight = !inHighlight;
          } else {
            currentText += part[i];
          }
        }
        
        // Add any remaining text
        if (currentText) {
          segments.push({
            text: currentText,
            highlighted: inHighlight
          });
        }
        
        return {
          type: 'compound',
          segments
        };
      } else {
        // No highlighting in this part
        return {
          type: 'simple',
          text: part,
          highlighted: false
        };
      }
    });
  };
  
  const processedParts = processTextForRendering();
  
  return (
    <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
      {processedParts.map((part, partIdx) => {
        if (part.type === 'simple') {
          // Simple non-highlighted word
          return (
            <BaseText
              key={partIdx}
              fontSize={fontSize}
              weight={notHighlightedWeight}
              lineHeight={lineHeight}
              style={{ color: notHighlightedColor }}
              className={cx(`${className} mr-4`, { 'text-grey-600': true })}
              {...rest}>
              {part.text?.trim()}
            </BaseText>
          );
        } else {
          // Compound word with highlights
          return (
            <View key={partIdx} style={{ flexDirection: 'row', marginRight: 4 }}>
              {part?.segments?.map((segment, segIdx) => (
                <BaseText
                  key={segIdx}
                  fontSize={fontSize}
                  weight={segment.highlighted ? highlightedWeight : notHighlightedWeight}
                  lineHeight={lineHeight}
                  style={{ color: segment.highlighted ? highlightedColor : notHighlightedColor }}
                  className={cx(`${className}`, { 
                    'text-grey-300': segment.highlighted, 
                    'text-grey-600': !segment.highlighted 
                  })}
                  {...rest}>
                  {segment.text.trim()}
                </BaseText>
              ))}
            </View>
          );
        }
      })}
    </View>
  );
};

export default HighlightedText;