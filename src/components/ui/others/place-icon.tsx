import React from 'react';
import { SFSymbol, SymbolView } from 'expo-symbols';
import { EXTENDED_PLACE_TYPE_ICONS } from '@services/mapbox';
import { colors } from '@theme/colors';

interface PlaceIconProps {
  placeType: string;
  size?: number;
  color?: string;
}

const PlaceIcon: React.FC<PlaceIconProps> = ({ 
  placeType, 
  size = 24, 
  color = colors.grey[700]
}) => {
  const iconName = EXTENDED_PLACE_TYPE_ICONS[placeType] || 'mappin.and.ellipse';

  return (
    <SymbolView
      name={iconName as SFSymbol}
      style={{
        width: size,
        height: size,
      }}
      type="monochrome"
      tintColor={color}
    />
  );
};

export default PlaceIcon;