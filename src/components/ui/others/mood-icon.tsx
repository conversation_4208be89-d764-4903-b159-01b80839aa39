import { colors } from '@theme/colors';
import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, withSpring } from 'react-native-reanimated';
import { MOOD_STATE, MOOD_VARIANT } from 'src/@types/app-interface';
import { wp } from '@utils/responsive-dimension';
import {
  AngryMoodE<PERSON>ji,
  CryMood<PERSON><PERSON>ji,
  HappyMood<PERSON><PERSON>ji,
  MeltMoodEmoji,
  NervousMoodEmoji,
  NeutralMoodEmoji,
  NoneMoodE<PERSON>ji,
  SmileMood<PERSON><PERSON>ji,
  <PERSON><PERSON>ood<PERSON><PERSON>ji,
  ConfusedMoodEmoji,
} from './icons';
import { cx } from '@utils/functions';

// Create an interface for the component props
interface MoodIconProps {
  mood: MOOD_VARIANT;
  state?: MOOD_STATE;
  onPress?: () => void;
  className?: string;
  size?: number;
  padding?: number;
  hasBorder?: boolean;
}

// Animated version of CircledIcon
const AnimatedCircledIcon = Animated.createAnimatedComponent(View);

export const MoodIcon: React.FC<MoodIconProps> = ({
  mood,
  state = MOOD_STATE.INACTIVE,
  onPress,
  className = '',
  size = wp(22),
  padding,
  hasBorder = true,
}) => {
  // Animation shared values
  const activeState = useSharedValue(state === MOOD_STATE.ACTIVE ? 1 : 0);
  const scaleValue = useSharedValue(1);

  // Update animation values when props change
  useEffect(() => {
    activeState.value = withTiming(state === MOOD_STATE.ACTIVE ? 1 : 0, { duration: 20 });
    // if (activeState.value === 1) {
    //   activeState.value = withTiming(state === MOOD_STATE.ACTIVE ? 1 : 0, { duration: 20 });
    // } else {
    //   activeState.value = withTiming(state === MOOD_STATE.ACTIVE ? 1 : 0, { duration: 300 });
    // }
  }, [state]);

  // Create animated styles
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: withSpring(activeState.value === 1 ? 0.95 : 0, { damping: 18, stiffness: 200, velocity: 4 }) },
      ],
      borderWidth: 2,
      borderStyle: 'dashed',
      zIndex: -1,
      opacity: withTiming(activeState.value, { duration: 70 }),
    };
  });

  // Create animated styles
  const iconAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: withSpring(activeState.value === 1 ? 1.12 : 1, { damping: 4, stiffness: 400 }) }],
    };
  });

  // Handle press animation
  const handlePress = () => {
    // Quick pulse animation on press
    scaleValue.value = 0.9;
    scaleValue.value = withSpring(1, { damping: 4, stiffness: 200 });

    // Call the provided onPress callback if it exists
    if (onPress) {
      onPress();
    }
  };

  const pressAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scaleValue.value }],
    };
  });

  // Get the appropriate icon based on the mood
  const IconComponent = moodIcons[mood].icon;

  return (
    // <Animated.View
    //   className="rounded-full items-center justify-center"
    //   style={[{ borderColor: moodIcons[mood].mainColor }, pressAnimatedStyle, animatedStyle]}>
    <Animated.View
      className={cx(`rounded-full justify-center items-center bg-white  ${className} overflow-visible`, {
        'p-2': hasBorder,
      })}
      style={iconAnimatedStyle}
      onTouchEnd={onPress ? handlePress : undefined}>
      <View
        className={`rounded-full justify-center items-center ${className}`}
        style={[{ backgroundColor: moodIcons[mood].bgColor, padding: padding || wp(8) }]}>
        {IconComponent(size)}
      </View>
      <Animated.View
        className="rounded-full items-center justify-center absolute"
        style={[
          { borderColor: moodIcons[mood].mainColor, height: size * 2.5, width: size * 2.5 },
          pressAnimatedStyle,
          animatedStyle,
        ]}
      />
    </Animated.View>
    // </Animated.View>
  );
};

const styles = StyleSheet.create({
  circleBase: {
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

// Type definition for the mood object
type MoodObject = {
  name: string;
  bgColor: string;
  mainColor: string;
  icon: (size: number) => React.ReactNode;
};

// Create the mood objects mapping
const moodIcons: Record<MOOD_VARIANT, MoodObject> = {
  [MOOD_VARIANT.NEUTRAL]: {
    name: 'Neutral',
    bgColor: colors.blue[50],
    mainColor: colors.blue[500],
    icon: (size: number) => <NeutralMoodEmoji size={size} color={colors.blue[500]} />,
  },
  [MOOD_VARIANT.SAD]: {
    name: 'Sad',
    bgColor: colors.orange[50],
    mainColor: colors.orange[500],
    icon: (size: number) => <SadMoodEmoji size={size} color={colors.orange[500]} />,
  },
  [MOOD_VARIANT.ANGRY]: {
    name: 'Angry',
    bgColor: colors.red[50],
    mainColor: colors.red[500],
    icon: (size: number) => <AngryMoodEmoji size={size} color={colors.red[500]} />,
  },
  [MOOD_VARIANT.NERVOUS]: {
    name: 'Nervous',
    bgColor: colors.orange[50],
    mainColor: colors.orange[500],
    icon: (size: number) => <NervousMoodEmoji size={size} color={colors.orange[500]} />,
  },
  [MOOD_VARIANT.CRY]: {
    name: 'Cry',
    bgColor: colors.pink[100],
    mainColor: colors.pink[500],
    icon: (size: number) => <CryMoodEmoji size={size} color={colors.pink[500]} />,
  },
  [MOOD_VARIANT.MELT]: {
    name: 'Melt',
    bgColor: colors.purple[50],
    mainColor: colors.purple[500],
    icon: (size: number) => <MeltMoodEmoji size={size} color={colors.purple[500]} />,
  },
  [MOOD_VARIANT.HAPPY]: {
    name: 'Happy',
    bgColor: colors.green[50],
    mainColor: colors.green[500],
    icon: (size: number) => <HappyMoodEmoji size={size} color={colors.green[500]} />,
  },
  [MOOD_VARIANT.CONFUSED]: {
    name: 'Confused',
    bgColor: colors.purple[50],
    mainColor: colors.purple[500],
    icon: (size: number) => <ConfusedMoodEmoji size={size} color={colors.purple[500]} />,
  },
  [MOOD_VARIANT.SMILE]: {
    name: 'Smile',
    bgColor: colors.blue[50],
    mainColor: colors.blue[500],
    icon: (size: number) => <SmileMoodEmoji size={size} color={colors.blue[500]} />,
  },
  [MOOD_VARIANT.NONE]: {
    name: 'None',
    bgColor: colors.grey[25],
    mainColor: colors.grey[300],
    icon: (size: number) => <NoneMoodEmoji size={size} color={colors.grey[300]} />,
  },
};
