import { ReactNode } from 'react';
import classNames from 'classnames';
import Row from '../layout/row';
import { RowProps } from '../layout/row';

export interface CircledIconProps extends Partial<RowProps> {
  children?: ReactNode;
  className?: string;
}

const CircledIcon = ({ children, className, ...rest }: CircledIconProps) => {
  return (
    <Row className={classNames(`p-10 rounded-full items-center justify-center ${className}`)} {...rest}>
      {children}
    </Row>
  );
};

export default CircledIcon;
