import { ReactNode } from 'react';
import { View, ViewProps } from 'react-native';
import cx from 'classnames';

export interface RowProps extends Partial<ViewProps> {
  children?: ReactNode;
  className?: string;
  disableSpread?: boolean;
  alignCenter?: boolean;
}

const Row = ({ children, className, disableSpread = false, alignCenter = true, ...props }: RowProps) => {
  return (
    <View
      className={cx(
        'flex flex-row',
        {
          'justify-between': !disableSpread,
          'items-center': alignCenter,
        },
        className,
      )}
      {...props}>
      {children}
    </View>
  );
};

export default Row;
