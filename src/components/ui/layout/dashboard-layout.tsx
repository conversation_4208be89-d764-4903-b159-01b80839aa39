import { ReactNode } from 'react';
import { ActivityIndicator, View } from 'react-native';
import CustomImage from '../others/custom-image';
import useInteractionWait from '@hooks/use-interaction-wait';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface DashboardLayoutProps {
  children?: ReactNode;
  customLoading?: ReactNode;
  isLoading?: boolean;
  showLoader?: boolean;
  insetTop?: boolean;
}

const DashboardLayout = ({ children, isLoading, showLoader = true, customLoading, insetTop = true }: DashboardLayoutProps) => {
  const { ready } = useInteractionWait();
  const { top } = useSafeAreaInsets();

  if ((!ready || isLoading) && showLoader === true) {
    return (
      <>
        {customLoading ? (
          customLoading
        ) : (
          <View style={{ paddingTop: insetTop ? top : 0 }} className="flex-1 bg-white">
            <View className="flex-1 items-center justify-center">
              <ActivityIndicator />
            </View>
          </View>
        )}
      </>
    );
  }

  return (
    <View style={{ paddingTop: insetTop ? top : 0 }} className="flex-1 bg-white">
      {children}
    </View>
  );
};

export default DashboardLayout;
