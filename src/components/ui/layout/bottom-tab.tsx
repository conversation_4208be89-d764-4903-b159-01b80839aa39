import Row from './row';
import Pressable from '../base/pressable';
import { colors } from '@theme/colors';
import { hp, wp } from '@utils/responsive-dimension';
import {
  SettingsIcon,
  DashboardIcon,
  AddIcon,
  BookmarkIcon,
  TrashIcon,
  CheckCircleIcon,
  BookmarkTickIcon,
} from '../others/icons';
import { BlurView } from 'expo-blur';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Dimensions, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MaskedView from '@react-native-masked-view/masked-view';
import Animated, {
  FadeIn,
  FadeOut,
  SharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import useDelayedProperty from '@hooks/use-delayed-property';
import { Check, TickCircle } from 'iconsax-react-native';
import { BaseText } from '../base';
import { useSelectionStore } from '@store/useSelectionStore';
import { useJournal } from '@hooks/use-journal-db-service';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { cx, delay } from '@utils/functions';
import { Instalog } from '@instalog.dev/expo';

const { width } = Dimensions.get('window');

interface BottomTabProps {
  isSelectionActive: SharedValue<boolean>;
  handleSelectEntry: (entry: any) => void;
}

const BottomTab = ({ isSelectionActive, handleSelectEntry }: BottomTabProps) => {
  const [actionType, setActionType] = useState<'delete' | 'bookmark' | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const { bottom } = useSafeAreaInsets();
  const navigation = useNavigation();
  const { selectedEntries, selectAll, deleteSelected } = useSelectionStore();
  const { entries, confirmAndDeleteEntries, loadEntries, removeEntries, isDbInitialized } = useJournal();

  useEffect(() => {
    loadEntries();
  }, [isDbInitialized, showFeedback]);

  const isSelectionActiveValue = useDelayedProperty(isSelectionActive, true as const, false as const, 0, 0);

  const appNavStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      width: '100%',
      opacity: withTiming(isSelectionActive?.value ? 0 : 1, { duration: 200 }),
      zIndex: isSelectionActive?.value ? 0 : 10,
      transform: [
        { scale: withSpring(isSelectionActive?.value ? 0.5 : 1, { damping: 18, stiffness: 200, velocity: 4 }) },
        { translateY: withSpring(isSelectionActive?.value ? 20 : 0, { damping: 18, stiffness: 200, velocity: 4 }) },
      ],
    };
  }, []);

  const selectionActiveStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      width: '100%',
      opacity: withTiming(isSelectionActive?.value ? 1 : 0, { duration: 200 }),
      zIndex: isSelectionActive?.value ? 10 : -1,
      transform: [
        { scale: withSpring(isSelectionActive?.value ? 1 : 0.5, { damping: 18, stiffness: 200, velocity: 4 }) },
        { translateY: withSpring(isSelectionActive?.value ? 0 : 20, { damping: 18, stiffness: 200, velocity: 4 }) },
      ],
    };
  }, []);

  const handleSelectAll = useCallback(
    (isDeselecting: boolean) => {
      const allEntryIds = entries.map(entry => entry.id);
      selectAll(isDeselecting ? [] : allEntryIds);
    },
    [entries, selectAll],
  );

  const isAllSelected = useMemo((): boolean => {
    if (selectedEntries.length !== entries?.length) {
      return false;
    }

    const selectedSet = new Set(selectedEntries);
    return entries.map(e => e.id).every(entry => selectedSet.has(entry));
  }, [entries, selectedEntries]);

  const handleDeleteSelected = async () => {
    Alert.alert(
      'Delete Selected Entries',
      'Are you sure you want to delete the selected entries? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel', onPress: () => {} },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => handleDelete(),
        },
      ],
    );

    const handleDelete = async () => {
      setActionType('delete');
      const res = await removeEntries(selectedEntries);
      if (res) {
        await delay(1000);
        setShowFeedback(true);
      }
      setTimeout(() => {
        setShowFeedback(false);
        setActionType(null);
      }, 5000);
    };
  };

  return (
    <View className="absolute bottom-0 w-full" style={{ height: hp(90), paddingBottom: bottom }}>
      {/* Feathered Blur via Mask */}
      <MaskedView
        style={StyleSheet.absoluteFillObject}
        maskElement={
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.5)', 'rgba(0,0,0,0.9)', 'black', 'black', 'black']}
            locations={[0, 0.2, 0.4, 0.6, 0.8, 1]}
            style={StyleSheet.absoluteFillObject}
          />
        }>
        <BlurView tint="light" intensity={100} style={StyleSheet.absoluteFillObject} shouldRasterizeIOS />
      </MaskedView>

      {/* Tab content */}
      <Animated.View
        style={[
          {
            paddingBottom: bottom > 0 ? bottom : 20,
          },
          appNavStyle,
        ]}
        className="w-full pb-16 pt-20 px-70 justify-between">
        <Row>
          <Pressable
            className="bg-grey-50 rounded-[57px] py-8 px-12"
            onPress={() => navigation.navigate('Settings')}>
            <SettingsIcon color={colors.grey[500]} size={wp(18)} />
          </Pressable>
          <Pressable
            // className="bg-blue-50 rounded-full p-15"
            className="bg-blue-50 rounded-[57px] py-8 px-12"
            onPress={() => {Instalog.;navigation.navigate('WriteJournalScreen')}}>
            <AddIcon color={colors.blue[500]} size={wp(18)} />
          </Pressable>
          {/* <Pressable
            className="bg-grey-50 rounded-[57px] py-8 px-12"
            onPress={() => navigation.navigate('DashboardScreen')}>
            <DashboardIcon color={colors.grey[500]} size={wp(18)} />
          </Pressable> */}
        </Row>
      </Animated.View>

      <Animated.View
        style={[
          {
            paddingBottom: bottom > 0 ? bottom : 20,
          },
          selectionActiveStyle,
        ]}
        className="w-full pb-16 pt-20 px-70 items-center">
        <Row disableSpread style={[selectionActiveStyle, { gap: wp(10) }]}>
          <Pressable
            className={cx('bg-grey-50 rounded-[57px] py-8 px-12', { 'opacity-30': selectedEntries.length < 1 })}
            disabled={selectedEntries.length < 1}
            onPress={handleDeleteSelected}>
            <TrashIcon color={colors.grey[500]} size={wp(18)} />
          </Pressable>
          <Pressable
            className={cx('rounded-full py-8 px-12', { 'bg-blue-50': isAllSelected, 'bg-grey-50': !isAllSelected })}
            onPress={() => handleSelectAll(isAllSelected)}>
            <Row disableSpread>
              <TickCircle variant="Bold" color={isAllSelected ? colors.blue[500] : colors.grey[500]} size={wp(20)} />
              <BaseText
                fontSize={15}
                weight="medium"
                className={cx('text-grey-400 ml-5', { 'text-blue-500': isAllSelected })}>
                {isAllSelected ? 'All Selected' : 'Select all'}
              </BaseText>
            </Row>
          </Pressable>
          <Pressable
            className={cx('bg-grey-50 rounded-[57px] py-8 px-12', { 'opacity-30': selectedEntries.length < 1 })}
            disabled={selectedEntries.length < 1}
            onPress={() => navigation.navigate('DashboardScreen')}>
            <BookmarkIcon color={colors.grey[500]} size={wp(18)} />
          </Pressable>
        </Row>
      </Animated.View>
      {showFeedback && (
        <Animated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}
          className="absolute bottom-0 right-0 left-0 items-center z-10">
          <MaskedView
            style={StyleSheet.absoluteFillObject}
            maskElement={
              <LinearGradient
                colors={['transparent', 'rgba(0,0,0,0.5)', 'rgba(0,0,0,0.5)']}
                locations={[0, 0.2, 0.4, 0.6, 0.8, 1]}
                style={StyleSheet.absoluteFillObject}
              />
            }>
            <BlurView tint="light" intensity={100} style={StyleSheet.absoluteFillObject} shouldRasterizeIOS />
          </MaskedView>
          {actionType === 'delete' && (
            <Row disableSpread className="rounded-full py-8 px-12 bg-red-50 mb-50">
              <TrashIcon color={colors.red[500]} size={wp(18)} />
              <BaseText fontSize={12} weight="medium" className={cx('text-red-500 ml-5')}>
                Entries Deleted
              </BaseText>
            </Row>
          )}
          {actionType === 'bookmark' && (
            <Row disableSpread className="rounded-full py-8 px-12 bg-green-50 mb-50">
              <BookmarkTickIcon color={colors.green[500]} size={wp(18)} />
              <BaseText fontSize={12} weight="medium" className={cx('text-green-500 ml-5')}>
                Entries bookmarked
              </BaseText>
            </Row>
          )}
        </Animated.View>
      )}
    </View>
  );
};

export default BottomTab;
