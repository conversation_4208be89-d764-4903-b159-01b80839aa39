import { ReactNode, useMemo } from 'react';
import { Text, TextProps } from 'react-native';
import { hp, wp } from '@utils/responsive-dimension';
import cx from 'classnames';

export type BaseTextProps = {
  fontSize?: number;
  lineHeight?: number;
  className?: string;
  children?: ReactNode;
  weight?: 'light' | 'regular' | 'medium' | 'bold' | 'black';
  textTransform?: 'uppercase' | 'lowercase' | 'capitalize';
  style?: { [key: string]: any };
} & Omit<TextProps, 'style'>;

// export interface BaseTextProps extends TextProps {
//   fontSize?: number;
//   type?: 'heading' | 'body';
//   classes?: string;
//   children?: ReactNode;
//   // weight?:
// }

const BaseText = ({
  className,
  weight = 'regular',
  children,
  fontSize = 13,
  lineHeight,
  textTransform,
  style,
  ...props
}: BaseTextProps) => {
  const responsiveFs = useMemo(() => wp(fontSize), []);

  return (
    <Text
      className={cx(textSettings.classes, textSettings.weight[weight] as any, className)}
      style={{
        fontSize: wp(fontSize),
        letterSpacing: textSettings.letterSpacing(responsiveFs),
        lineHeight: lineHeight ? hp(lineHeight) : undefined,
        textTransform: textTransform,
        ...style,
      }}
      {...props}>
      {children}
    </Text>
  );
};

const textSettings = {
  classes: 'leading-normal',
  letterSpacing: (size: number) => 0,
  weight: {
    light: 'font-circularLight',
    regular: 'font-circularBook',
    medium: 'font-circularMedium',
    bold: 'font-circularBold',
    black: 'font-circularBlack',
  },
};

export default BaseText;
