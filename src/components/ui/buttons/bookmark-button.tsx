import React from 'react';
import Pressable, { PressableProps } from '../base/pressable';
import { BookmarkIcon } from '../others/icons';
import { wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';

interface BookmarkButtonProps extends PressableProps {
  size?: number;
  isBookmarked?: boolean;
}

const BookmarkButton = ({ 
  size = wp(18), 
  isBookmarked = false,
  ...props 
}: BookmarkButtonProps) => {
  const color = isBookmarked ? colors.purple[500] : colors.grey[300];
  
  return (
    <Pressable {...props}>
      <BookmarkIcon size={size} color={color} />
    </Pressable>
  );
};

export default BookmarkButton;
