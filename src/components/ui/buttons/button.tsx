import React, { ReactNode } from 'react';
import { BaseText } from '../base';
import cx from 'classnames';
import Pressable, { PressableProps } from '../base/pressable';
import Animated, { interpolate, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

export interface ButtonProps extends PressableProps {
  text: string;
  className?: string;
  textColor?: TextColor;
  negative?: boolean;
  variant?: ButtonVariant;
  disabled?: boolean;
  leftIcon?: ReactNode;
}

export enum ButtonVariant {
  'PRIMARY' = 'Primary',
  'PRIMARY_LIGHT' = 'Primary_Light',
  'LIGHT' = 'Light',
}

export enum TextColor {
  'BLACK' = 'Black',
  'WHITE' = 'White',
}

const DURATION = 60;

const Button = ({
  text,
  disabled,
  variant = ButtonVariant.PRIMARY,
  textColor = TextColor.WHITE,
  leftIcon,
  className,
  ...rest
}: ButtonProps) => {
  const transition = useSharedValue(0);
  const isActive = useSharedValue(true);

  const style = useAnimatedStyle(() => ({
    transform: [{ scale: interpolate(transition.value, [0, 1], [1, 0.95]) }],
  }));

  return (
    <Pressable
      disabled={disabled}
      className='w-auto'
      onPressIn={() => {
        isActive.value = true;
        transition.value = withTiming(1, { duration: DURATION }, () => {
          if (!isActive.value) {
            transition.value = withTiming(0, { duration: DURATION });
          }
        });
      }}
      onPressOut={() => {
        if (transition.value === 1) {
          transition.value = withTiming(0, { duration: DURATION });
        }
        isActive.value = false;
      }}
      activeOpacity={1}
      {...rest}>
      <Animated.View
        className={cx(className, variantStyle[variant].classes, 'flex-row items-center justify-center', { ' bg-disabledBtn': disabled }, btnBaseStyle)}
        style={[style]}>
          {leftIcon}
        <BaseText
          fontSize={16}
          lineHeight={22}
          className={cx('text-center', {
            'text-grey-500': variant === ButtonVariant.LIGHT || textColor === TextColor.BLACK,
            'text-white': variant === ButtonVariant.PRIMARY && textColor === TextColor.WHITE,
            'text-purple-500': variant === ButtonVariant.PRIMARY_LIGHT,
            'ml-8': leftIcon,
          })}>
          {text}
        </BaseText>
      </Animated.View>
    </Pressable>
  );
};

const btnBaseStyle = 'flex-row items-center justify-center rounded-full py-12 px-25 w-auto';

const variantStyle = {
  [ButtonVariant.PRIMARY]: { classes: 'bg-primary' },
  [ButtonVariant.PRIMARY_LIGHT]: { classes: 'bg-purple-50' },
  [ButtonVariant.LIGHT]: { classes: 'bg-grey-25' },
};

export default Button;
