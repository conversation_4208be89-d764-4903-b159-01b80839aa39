import React, { ReactNode } from 'react';
import { Platform, SafeAreaView, View, ViewProps, ViewStyle } from 'react-native';
import Modal, { ModalProps } from 'react-native-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { hp, wp } from '@utils/responsive-dimension';

export interface BottomModalProps extends Partial<ModalProps> {
  children?: ReactNode;
  closeModal: () => void;
  showButton?: boolean;
  modalStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  innerStyle?: ViewStyle;
  contentContainerClass?: ViewProps['className'];
}

const BottomModal = ({
  children,
  showButton = true,
  modalStyle,
  containerStyle,
  closeModal = () => {},
  contentContainerClass,
  ...rest
}: BottomModalProps) => {
  const insetBottom = useSafeAreaInsets().bottom;
  const insertTop = useSafeAreaInsets().top + hp(12);

  return (
    <Modal
      avoidKeyboard
      onBackdropPress={() => closeModal()}
      backdropColor="#1E1E1E80"
      hideModalContentWhileAnimating
      // useNativeDriverForBackdrop
      onBackButtonPress={() => closeModal!()}
      style={[{ flex: 1, justifyContent: 'flex-end', margin: 0 }, modalStyle]}
      {...rest}>
      <SafeAreaView style={{ marginBottom: insetBottom < 1 ? 16 : 0 }}>
        <View
          style={[{ justifyContent: 'flex-end', marginTop: insertTop, marginHorizontal: wp(16) }, containerStyle]}
          className={`bg-white rounded-[46px] ${contentContainerClass}`}>
          <View className="h-7 w-50 bg-grey-50 my-10 self-center rounded-full" />
          {children}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default BottomModal;
