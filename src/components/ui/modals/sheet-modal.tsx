import { colors } from '@theme/colors';
import React, { ReactNode } from 'react';
import { View } from 'react-native';
import { Modal, ModalProps } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export interface SheetModalProps extends Partial<ModalProps> {
  children?: ReactNode;
  closeModal: () => void;
}

const SheetModal = ({ children, closeModal = () => {}, ...rest }: SheetModalProps) => {
  const insetBottom = useSafeAreaInsets().bottom;

  return (
    <Modal
      presentationStyle="pageSheet"
      animationType="slide"
      onDismiss={() => closeModal()}
      onRequestClose={closeModal}
      style={{backgroundColor: colors.grey[25]}}
      {...rest}>
      <View style={{ paddingBottom: insetBottom < 1 ? 16 : 0, flex: 1 }} className="bg-grey-25">
        <View className="h-7 w-50 bg-grey-50 my-10 self-center rounded-full" />
        {children}
      </View>
    </Modal>
  );
};

export default SheetModal;
