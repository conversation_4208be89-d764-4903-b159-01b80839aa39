import React, { useState } from 'react';
import { View, Platform } from 'react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import BottomModal, { BottomModalProps } from './bottom-modal';
import { BaseText } from '../base';
import { colors } from '@theme/colors';
import { hp } from '@utils/responsive-dimension';
import Row from '../layout/row';

interface DateSelectorModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onDateTimeSelect: (newDate?: Date, newTime?: Date) => void;
  initialDate?: Date;
  initialTime?: Date;
}

const DateSelectorModal = ({
  closeModal,
  onDateTimeSelect,
  initialDate = new Date(),
  initialTime = new Date(),
  ...props
}: DateSelectorModalProps) => {
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [selectedTime, setSelectedTime] = useState(initialTime);

  const handleDateChange = (event: DateTimePickerEvent, date?: Date) => {
    if (date) {
      setSelectedDate(date);
      onDateTimeSelect(date, undefined);
    }
  };

  const handleTimeChange = (event: DateTimePickerEvent, date?: Date) => {
    if (date) {
      setSelectedTime(date);
      onDateTimeSelect(undefined, date);
    }
  };

  const handleUpdate = () => {
    onDateTimeSelect(selectedDate, selectedTime);
    closeModal();
  };

  return (
    <BottomModal {...props} closeModal={closeModal} onModalHide={handleUpdate}>
      <View className="px-20 pb-24">
        <BaseText fontSize={16} weight="medium" className="text-grey-500 py-10">
          Date of Entry
        </BaseText>

        <DateTimePicker
          value={selectedDate}
          mode="date"
          onChange={handleDateChange}
          textColor={colors.grey[500]}
          maximumDate={new Date()}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          style={{
            height: Platform.OS === 'ios' ? hp(210) : 'auto',
            width: '100%',
          }}
        />

        <Row className="mt-10">
          <BaseText fontSize={16} weight="medium" className="text-grey-500 py-10">
            Time of Entry
          </BaseText>

          <DateTimePicker
            value={selectedTime}
            mode="time"
            is24Hour={true}
            onChange={handleTimeChange}
            textColor={colors.grey[500]}
            display={Platform.OS === 'ios' ? 'compact' : 'default'}
            style={{
              height: Platform.OS === 'ios' ? hp(210) : 'auto',
              width: '100%',
            }}
          />
        </Row>
      </View>
    </BottomModal>
  );
};

export default DateSelectorModal;
