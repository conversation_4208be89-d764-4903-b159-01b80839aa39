import React, { useState } from 'react';
import { View, Platform } from 'react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import BottomModal, { BottomModalProps } from './bottom-modal';
import { BaseText } from '../base';
import Button from '../buttons/button';
import dayjs from 'dayjs';
import { colors } from '@theme/colors';
import { hp } from '@utils/responsive-dimension';

interface TimeSelectorModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onTimeSelect: (date: Date) => void;
  initialTime?: Date;
}

const TimeSelectorModal = ({
  closeModal,
  onTimeSelect,
  initialTime = new Date(),
  ...props
}: TimeSelectorModalProps) => {
  const [selectedTime, setSelectedTime] = useState(initialTime);

  const handleTimeChange = (event: DateTimePickerEvent, date?: Date) => {
    if (Platform.OS === 'android') {
      if (event.type === 'set' && date) {
        setSelectedTime(date);
      }
    } else {
      date && setSelectedTime(date);
    }
  };

  const handleSave = () => {
    onTimeSelect(selectedTime);
    // closeModal();
  };

  return (
    <BottomModal {...props} closeModal={closeModal}>
      <View className="px-20 pb-20">
        <BaseText fontSize={16} weight="medium" className="text-grey-500 py-10">
          Time of Entry
        </BaseText>

        <DateTimePicker
          value={selectedTime}
          mode="time"
          is24Hour={true}
          onChange={handleTimeChange}
          textColor={colors.grey[500]}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          style={{
            height: Platform.OS === 'ios' ? hp(210) : 'auto',
            width: '100%',
          }}
        />

        {/* <Button 
          text="Confirm Time"
          className="mt-20"
          onPress={handleSave}
        /> */}
      </View>
    </BottomModal>
  );
};

export default TimeSelectorModal;
