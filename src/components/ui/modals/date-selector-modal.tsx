import React, { useState } from 'react';
import { View, Platform } from 'react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import BottomModal, { BottomModalProps } from './bottom-modal';
import { BaseText } from '../base';
import Button from '../buttons/button';
import dayjs from 'dayjs';
import { colors } from '@theme/colors';
import { hp } from '@utils/responsive-dimension';

interface DateSelectorModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onDateSelect: (date: Date) => void;
  initialDate?: Date;
}

const DateSelectorModal = ({
  closeModal,
  onDateSelect,
  initialDate = new Date(),
  ...props
}: DateSelectorModalProps) => {
  const [selectedDate, setSelectedDate] = useState(initialDate);

  const handleDateChange = (event: DateTimePickerEvent, date?: Date) => {
    if (Platform.OS === 'android') {
      if (event.type === 'set' && date) {
        setSelectedDate(date);
      }
    } else {
      date && setSelectedDate(date);
    }
  };

  const handleSave = () => {
    onDateSelect(selectedDate);
    closeModal();
  };

  return (
    <BottomModal {...props} closeModal={closeModal}>
      <View className="px-20 pb-20">
        <BaseText fontSize={16} weight="medium" className="text-grey-500 py-10">
          Date of Entry
        </BaseText>

        <DateTimePicker
          value={selectedDate}
          mode="date"
          onChange={handleDateChange}
          textColor={colors.grey[500]}
          maximumDate={new Date()}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          style={{
            height: Platform.OS === 'ios' ? hp(210) : 'auto',
            width: '100%',
          }}
        />

        <Button 
          text="Confirm Date"
          className="mt-20"
          onPress={handleSave}
        />
      </View>
    </BottomModal>
  );
};

export default DateSelectorModal;