import { View } from 'react-native';
import BottomModal, { BottomModalProps } from './bottom-modal';
import { BaseText } from '../base';
import { MOOD_STATE, MOOD_VARIANT } from 'src/@types/app-interface';
import { MoodIcon } from '../others/mood-icon';
import { wp } from '@utils/responsive-dimension';
import Button from '../buttons/button';
import * as Sensors from 'expo-sensors';
import { useEffect } from 'react';
import Animated, {
  Easing,
  interpolate,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import CustomImage from '../others/custom-image';
import { BlurView } from 'expo-blur';
import { Blur, BlurMask, Canvas, Group, Image, useImage } from '@shopify/react-native-skia';
import { screenWidth } from '@utils/functions';

interface SubscribeModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  isEdit?: boolean;
  selectedMood?: MOOD_VARIANT;
  onSelectMood?: (mood: MOOD_VARIANT) => void;
}

const SubscribeModal = ({ closeModal, selectedMood, onSelectMood, ...props }: SubscribeModalProps) => {
  const gyroValue = useSharedValue({ x: 0, y: 0, z: 0 });
  const prev = useSharedValue({ x: 0, y: 0 });

  const derivedTranslations = useDerivedValue(() => {
    'worklet';
    const MAX_X = 40;
    const MAX_Y = 40;

    let newX = prev.value.x + gyroValue.value.y * -2;
    let newY = prev.value.y + gyroValue.value.x * -2;

    // Can be more cleaner
    if (Math.abs(newX) >= MAX_X) {
      newX = prev.value.x;
    }
    if (Math.abs(newY) >= MAX_Y) {
      newY = prev.value.y;
    }
    prev.value = {
      x: newX,
      y: newY,
    };
    return {
      x: newX,
      y: newY,
    };
  }, [gyroValue.value]);

  const AnimatedStyles = {
    motion: useAnimatedStyle(() => {
      const rotateX = interpolate(
        derivedTranslations.value.x,
        [-100, 0, 100],
        [-10, 0, 10],
        'clamp'
      )
      const rotateY = interpolate(
        derivedTranslations.value.y,
        [-100, 0, 100],
        [-10, 0, 10],
        'clamp'
      )

      return {
        zIndex: 3000,
        transform: [
          {
            translateX: withSpring(
              interpolate(
                derivedTranslations.value.x,
                [-100, 0, 100],
                [-10, 0, 10],
                'clamp'
              ),
            ),
          },
          {
            translateY: withSpring(
              interpolate(
                derivedTranslations.value.y,
                [-100, 0, 100],
                [-10, 0, 10],
                'clamp'
              ),
            ),
          },
          {
            perspective: 200,
          },
          // {
          //   rotateX: `${rotateX}deg`,
          // },
          // {
          //   rotateY: `${rotateY}deg`,
          // },
        ],
      };
    }),
  };

  useEffect(() => {
    const gyroscope = Sensors.Gyroscope.addListener(gyroscopeData => {
      // console.log(gyroscopeData);
      gyroValue.value = { x: gyroscopeData.x, y: gyroscopeData.y, z: gyroscopeData.z };
    });

    const _unsubscribe = () => {
      gyroscope.remove();
    };
    return () => _unsubscribe();
  }, []);

  const image = useImage(require('../../../assets/images/tempSub.png'));

  return (
    <BottomModal {...props} closeModal={closeModal}>
      <View className="mb-20 px-0">
        <Canvas
          style={{
            height: 250,
            width: '100%',
            position: 'absolute',
            top: -20
          }}>
          <Image image={image} x={10} y={40} fit="fitWidth" width={screenWidth - 60} height={190} />
          <Blur blur={5} />
        </Canvas>
        <Animated.View className="w-full h-[190px]" style={[AnimatedStyles.motion]}>
            <CustomImage
              imageProps={{ source: require('@assets/images/tempSub.png'), contentFit: 'contain' }}
              className="w-full h-full"
            />
        </Animated.View>
        {/* <View className="">
          <View>
            <CustomImage
              imageProps={{ source: require('@assets/images/tempSub.png'), contentFit: 'contain' }}
              className="w-full h-[190px]"
            />
            <BlurView className="absolute top-0 right-0 left-0 bottom-0 w-full h-full" intensity={10} tint="light" />
          </View>
          
        </View> */}
        <View className="flex-row items-center justify-center flex-wrap mt-18" style={{ gap: wp(12) }}></View>
        <Button className="self-center mt-18 px-40" text="Subscribe for $2/month" />
      </View>
    </BottomModal>
  );
};

export default SubscribeModal;
