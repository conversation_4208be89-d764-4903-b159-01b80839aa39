import { View } from 'react-native';
import BottomModal, { BottomModalProps } from './bottom-modal';
import { BaseText } from '../base';
import { MOOD_STATE, MOOD_VARIANT } from 'src/@types/app-interface';
import { MoodIcon } from '../others/mood-icon';
import { wp } from '@utils/responsive-dimension';
import Button from '../buttons/button';
import { useState } from 'react';

interface SelectMoodModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  isEdit?: boolean;
  selectedMood?: MOOD_VARIANT;
  onSelectMood?: (mood: MOOD_VARIANT) => void;
}

const SelectMoodModal = ({ closeModal, selectedMood, onSelectMood, ...props }: SelectMoodModalProps) => {
  const [pickedMood, setPickedMood] = useState(selectedMood);

  const moodMapped = Object.entries(MOOD_VARIANT)
    .map(i => i[1])
    .reverse();

  const activeMood = pickedMood ?? selectedMood;

  return (
    <BottomModal {...props} closeModal={closeModal}>
      <View className="mb-20 px-20">
        <BaseText fontSize={14} className="text-center text-grey-200">
          How do you feel right now?
        </BaseText>
        <BaseText fontSize={24} weight="medium" className="text-center text-grey-700 mt-14">
          {activeMood}
        </BaseText>
        <View className="flex-row items-center justify-center flex-wrap mt-18" style={{ gap: wp(12) }}>
          {moodMapped.map(i => (
            <MoodIcon
              key={i}
              mood={i}
              padding={wp(12)}
              size={wp(24)}
              onPress={() => setPickedMood(i)}
              state={activeMood === i ? MOOD_STATE.ACTIVE : MOOD_STATE.INACTIVE}
            />
          ))}
        </View>
        <Button
          className="self-center mt-18 px-40"
          text="Save to Entry."
          onPress={() => {
            onSelectMood?.(pickedMood ?? MOOD_VARIANT.HAPPY);
            closeModal();
          }}
        />
      </View>
    </BottomModal>
  );
};

export default SelectMoodModal;
