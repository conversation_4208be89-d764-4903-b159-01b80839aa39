import { View, TextInput } from 'react-native';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import AvoidKeyboard from '@components/ui/layout/avoid-keyboard';
import Row from '@components/ui/layout/row';
import { BookmarkIcon, CancelIcon, SearchIcon } from '@components/ui/others/icons';
import { wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import Button, { ButtonVariant } from '@components/ui/buttons/button';

interface SearchOverlayProps {
  searchContainerStyle: any;
  inputStyle: any;
  onCancelSearch: () => void;
}

const SearchOverlay = ({ searchContainerStyle, inputStyle, onCancelSearch }: SearchOverlayProps) => {
  return (
    <Animated.View className="flex-1" style={[searchContainerStyle]}>
      <SafeAreaView className="flex-1">
        <AvoidKeyboard keyboardVerticalOffset={16}>
          <View className="flex-1 justify-center items-center">
            <Row className="min-w-[70%]">
              <SearchIcon size={wp(18)} primaryColor={colors.grey[300]} secondaryColor={colors.grey[300]} />
              <Animated.View className="flex-1 mx-6" style={inputStyle}>
                <TextInput
                  testID="search-input"
                  placeholder="Search for your words"
                  autoFocus
                  className="font-circularBook text-grey-400"
                  placeholderTextColor={colors.grey[100]}
                  style={[{ fontSize: wp(16), color: colors.grey[700] }]}
                  returnKeyType="search"
                />
              </Animated.View>
            </Row>
          </View>
          <Row disableSpread className="self-center gap-x-10">
            <Button
              testID="cancel-search-button"
              variant={ButtonVariant.LIGHT}
              text="Cancel"
              onPress={onCancelSearch}
              leftIcon={<CancelIcon size={wp(18)} color={colors.grey[400]} />}
            />
            <Button
              variant={ButtonVariant.LIGHT}
              text="Bookmarked"
              leftIcon={<BookmarkIcon size={wp(18)} color={colors.grey[400]} />}
            />
          </Row>
        </AvoidKeyboard>
      </SafeAreaView>
    </Animated.View>
  );
};

export default SearchOverlay;