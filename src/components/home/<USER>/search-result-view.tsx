import { View, TextInput, ScrollView } from 'react-native';
import Animated from 'react-native-reanimated';
import Row from '@components/ui/layout/row';
import { SearchIcon } from '@components/ui/others/icons';
import { hp, wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import { BaseText } from '@components/ui/base';
import SearchEntryResultCard from '@components/cards/search-entry-result-card';
import { JournalEntry } from '@services/db/schema';
import { useCallback, useEffect, useState } from 'react';
import { useJournal } from '@hooks/use-journal-db-service';
import HighlightedText from '@components/ui/others/highlighted-text';

interface SearchResultViewProps {
  inputStyle: any;
  searchQuery: string;
  onSearchChange: (text: string) => void;
}

const SearchResultView = ({ inputStyle, searchQuery, onSearchChange }: SearchResultViewProps) => {
  const [searchResults, setSearchResults] = useState<JournalEntry[]>([]);

  const { searchEntries } = useJournal();

  const handleSearchJournalEntries = useCallback(async () => {
    console.log(searchQuery);
    const result = await searchEntries(searchQuery);
    setSearchResults(result);
  }, [searchEntries, searchQuery]);

  useEffect(() => {
    handleSearchJournalEntries();
  }, [handleSearchJournalEntries]);

  return (
    <View className="flex-1 bg-white">
      <Row className="px-20">
        <SearchIcon size={wp(18)} primaryColor={colors.grey[300]} secondaryColor={colors.grey[300]} />
        <Animated.View className="flex-1 mx-6" style={inputStyle}>
          <TextInput
            testID="search-input"
            placeholder="Search..."
            value={searchQuery}
            onChangeText={onSearchChange}
            onSubmitEditing={handleSearchJournalEntries}
            className="font-circularBook text-grey-400"
            placeholderTextColor={colors.grey[100]}
            style={[{ fontSize: wp(16), color: colors.grey[700] }]}
            returnKeyType="search"
          />
        </Animated.View>
      </Row>
      <ScrollView>
        {searchResults?.length > 0 && (
          <View className="px-20 mt-15">
            <BaseText fontSize={24} weight="medium">
              Search Results
            </BaseText>
            <HighlightedText
              // className="text-center"
              fontSize={16}
              lineHeight={22}
              highlightedColor={colors.blue[500]}
              notHighlightedColor={colors.grey[300]}
              highlightedWeight="medium"
              notHighlightedWeight="regular"
              text={`Here are *${searchResults?.length}* search results for *“${searchQuery}”*`}
            />
          </View>
        )}
        <View className="px-20 mt-15" style={{ gap: hp(10) }}>
          {searchResults.length > 0 ? (
            searchResults.map((entry, idx) => (
              <SearchEntryResultCard key={idx} entry={entry} searchQuery={searchQuery} />
            ))
          ) : (
            <View className="items-center py-30">
              <BaseText fontSize={16} className="text-grey-300 text-center">
                {searchQuery ? 'No entries found matching your search' : 'Type to search your journal entries'}
              </BaseText>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default SearchResultView;
