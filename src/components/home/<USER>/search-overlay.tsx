import { View, TextInput } from 'react-native';
import Animated, { useAnimatedStyle, SharedValue, withTiming } from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import AvoidKeyboard from '@components/ui/layout/avoid-keyboard';
import Row from '@components/ui/layout/row';
import { BookmarkIcon, CancelIcon, SearchIcon } from '@components/ui/others/icons';
import { hp, wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import Button, { ButtonVariant } from '@components/ui/buttons/button';
import { useState } from 'react';
import { useJournal } from '@hooks/use-journal-db-service';
import BookmarkView from './bookmark-view';
import SearchResultView from './search-result-view';

interface SearchOverlayProps {
  isSearchState: SharedValue<boolean>;
}

type activeView = 'search' | 'searchResult' | 'bookmark';

const SearchOverlay = ({ isSearchState }: SearchOverlayProps) => {
  const [activeView, setActiveView] = useState<activeView>('search');
  const [searchQuery, setSearchQuery] = useState<string>('');

  const { getBookmarkedEntries } = useJournal();

  const onPressCancel = () => {
    isSearchState.value = false;
    setActiveView('search');
  };

  const searchContainerStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isSearchState.value ? 1 : 0, { duration: 150 }),
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: isSearchState.value ? 10 : -10,
    };
  }, []);

  const inputStyle = useAnimatedStyle(() => {
    return {
      display: isSearchState.value ? 'flex' : 'none',
    };
  }, []);

  return (
    <Animated.View className="flex-1 bg-white" style={[searchContainerStyle]}>
      <SafeAreaView className="flex-1">
        <AvoidKeyboard keyboardVerticalOffset={16}>
          {activeView === 'search' && (
            <View className="flex-1 justify-center items-center">
              <Row className="min-w-[70%]">
                <SearchIcon size={wp(18)} primaryColor={colors.grey[300]} secondaryColor={colors.grey[300]} />
                <Animated.View className="flex-1 mx-6" style={inputStyle}>
                  <TextInput
                    testID="search-input"
                    placeholder="Search for your words"
                    autoFocus
                    className="font-circularBook text-grey-400"
                    placeholderTextColor={colors.grey[100]}
                    onChangeText={setSearchQuery}
                    value={searchQuery}
                    onSubmitEditing={() => setActiveView('searchResult')}
                    style={[{ fontSize: wp(16), color: colors.grey[700] }]}
                    returnKeyType="search"
                  />
                </Animated.View>
              </Row>
            </View>
          )}

          {activeView === 'searchResult' && (
            <SearchResultView inputStyle={inputStyle} searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          )}
          {activeView === 'bookmark' && <BookmarkView inputStyle={inputStyle} />}
          <Row disableSpread className="self-center gap-x-10">
            <Button
              testID="cancel-search-button"
              variant={ButtonVariant.LIGHT}
              text="Cancel"
              onPress={onPressCancel}
              leftIcon={<CancelIcon size={wp(18)} color={colors.grey[400]} />}
            />
            <Button
              variant={activeView === 'bookmark' ? ButtonVariant.PRIMARY_LIGHT : ButtonVariant.LIGHT}
              text="Bookmarked"
              onPress={() => setActiveView('bookmark')}
              leftIcon={
                <BookmarkIcon size={wp(18)} color={activeView === 'bookmark' ? colors.purple[500] : colors.grey[400]} />
              }
            />
          </Row>
        </AvoidKeyboard>
      </SafeAreaView>
    </Animated.View>
  );
};

export default SearchOverlay;
