import { View, TextInput, ScrollView } from 'react-native';
import Animated from 'react-native-reanimated';
import Row from '@components/ui/layout/row';
import { SearchIcon } from '@components/ui/others/icons';
import { hp, wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import { BaseText } from '@components/ui/base';
import SearchEntryResultCard from '@components/cards/search-entry-result-card';
import { JournalEntry } from '@services/db/schema';
import { useEffect, useState } from 'react';
import { useJournal } from '@hooks/use-journal-db-service';

interface BookmarkViewProps {
  inputStyle: any;
}

const BookmarkView = ({ inputStyle }: BookmarkViewProps) => {
  const [bookmarkedEntries, setBookmarkedEntries] = useState<JournalEntry[]>([]);

  const { getBookmarkedEntries, isDbInitialized } = useJournal();

  const handleGetBookmarkedEntries = async () => {
    const result = await getBookmarkedEntries();
    setBookmarkedEntries(result);
  };

  useEffect(() => {
    handleGetBookmarkedEntries();
  }, [isDbInitialized]);

  return (
    <View className="flex-1 bg-white">
      <Row className="px-20">
        <SearchIcon size={wp(18)} primaryColor={colors.grey[300]} secondaryColor={colors.grey[300]} />
        <Animated.View className="flex-1 mx-6" style={inputStyle}>
          <TextInput
            testID="search-input"
            placeholder="Search..."
            className="font-circularBook text-grey-400"
            placeholderTextColor={colors.grey[100]}
            style={[{ fontSize: wp(16), color: colors.grey[700] }]}
            returnKeyType="search"
          />
        </Animated.View>
      </Row>
      <View className="px-20 mt-15">
        <BaseText fontSize={24} weight="medium">
          Bookmarked
        </BaseText>
      </View>
      <ScrollView>
        <View className="flex-1 px-20 mt-5" style={{ gap: hp(10) }}>
          {bookmarkedEntries.map((i, idx) => (
            <SearchEntryResultCard key={idx} entry={i} />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default BookmarkView;
