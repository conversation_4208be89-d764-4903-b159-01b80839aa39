import DayEntriesCard from '@components/cards/day-entries-card';
import { BaseText } from '@components/ui/base';
import { useJournal } from '@hooks/use-journal-db-service';
import dayjs from 'dayjs';
import { useEffect, useLayoutEffect } from 'react';
import { View } from 'react-native';
import { SharedValue } from 'react-native-reanimated';

interface DayEntriesSectionProps {
  isSelectionActive: SharedValue<boolean>;
  handleSelectEntry: (entry: any) => void;
}

const DayEntriesSection = ({ isSelectionActive, handleSelectEntry }: DayEntriesSectionProps) => {
  const { entriesByMonth, isDbInitialized, loadEntries } = useJournal();

  // const hasEntries = entriesByDay?.length > 0;

  useEffect(() => {
    loadEntries();
  }, [isDbInitialized]);

  return (
    <>
      {entriesByMonth?.map((i, idx) => (
        <View className="flex-1 mt-16 px-16" key={idx}>
          <BaseText fontSize={24} weight="bold" className="text-grey-800">
            {dayjs(i.month).format('MMMM YYYY')}
          </BaseText>
          {i.entriesByDay?.map((j, idx) => (
            <DayEntriesCard
              key={idx}
              entries={j}
              isSelectionActive={isSelectionActive}
              handleSelectEntry={handleSelectEntry}
            />
          ))}
        </View>
      ))}
    </>
  );
};

export default DayEntriesSection;
