import JournalEntryCard from '@components/cards/journal-entry-card';
import { BaseText } from '@components/ui/base';
import { useJournal } from '@hooks/use-journal-db-service';
import { useNavigation } from '@react-navigation/native';
import { hp } from '@utils/responsive-dimension';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { View } from 'react-native';
import { SharedValue } from 'react-native-reanimated';
import useDelayedProperty from '@hooks/use-delayed-property';
import { useSelectionStore } from '@store/useSelectionStore';

interface TodaySectionProps {
  isSelectionActive: SharedValue<boolean>;
  handleSelectEntry: (entry: any) => void;
}

const TodaySection = ({ isSelectionActive, handleSelectEntry }: TodaySectionProps) => {
  const { todayEntries } = useJournal();
  const navigation = useNavigation();
  const { isEntrySelected } = useSelectionStore();

  const todayHasEntry = todayEntries?.length > 0;
  const isSelectionActiveValue = useDelayedProperty(isSelectionActive!, true as const, false as const, 0, 0);

  return (
    <View className="flex-1 pt-12 px-16" style={{ display: todayHasEntry ? 'flex' : 'none' }}>
      <BaseText fontSize={40} weight="bold" className="text-grey-800">
        Today
      </BaseText>
      <BaseText fontSize={16} className="text-grey-300">
        {dayjs().format('Do MMMM YYYY')}
      </BaseText>
      <View className="mt-15" style={{ gap: hp(20) }}>
        {todayEntries?.map?.((i, idx) => (
          <JournalEntryCard
            key={idx}
            item={i}
            hasBorder={false}
            isSelectionActive={isSelectionActive}
            selected={isEntrySelected(i.id)}
            onPress={() => {
              if (isSelectionActiveValue) {
                handleSelectEntry(i.id);
                return;
              }

              navigation.navigate('WriteJournalScreen', {
                journalId: i.id,
                images: i.images,
                title: i.title,
                entry_date: i.entry_date,
              });
            }}
          />
        ))}
      </View>
    </View>
  );
};

export default TodaySection;
