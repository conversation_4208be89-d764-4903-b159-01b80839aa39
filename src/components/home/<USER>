import { Dimensions, View } from 'react-native';
import Row from '@components/ui/layout/row';
import { CheckCircleIcon, DashboardIcon, SearchIcon } from '@components/ui/others/icons';
import { wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import CircledIcon from '@components/ui/others/circled-icon';
import CustomImage from '@components/ui/others/custom-image';
import { BaseText } from '@components/ui/base';
import Pressable from '@components/ui/base/pressable';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { screenWidth } from '@utils/functions';
import { TickCircle } from 'iconsax-react-native';
import Animated, {
  interpolate,
  runOnJS,
  SharedValue,
  useAnimatedReaction,
  useAnimatedStyle,
  useDerivedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import useDelayedProperty from '@hooks/use-delayed-property';

interface HomeHeaderProps {
  isSelectionActive: SharedValue<boolean>;
  onPressSelectionActive: () => void;
}

const HomeHeader = ({ isSelectionActive, onPressSelectionActive }: HomeHeaderProps) => {
  const { top } = useSafeAreaInsets();
  return (
    <Row className="w-full px-16 pb-16" style={{ gap: wp(4), paddingTop: top, zIndex: 100000 }}>
      <CustomImage
        imageProps={{
          source: require('@assets/images/blur-bg-top.png'),
          contentFit: 'cover',
        }}
        style={{ width: screenWidth, zIndex: -1 }}
        className="absolute top-0 right-0 left-0 bottom-0 w-full h-auto"
      />
      <SearchIcon size={wp(18)} primaryColor={colors.grey[300]} secondaryColor={colors.grey[300]} />
      <View className="flex-1 mx-6">
        <BaseText className="text-grey-300">Pull to search...</BaseText>
      </View>
      <Row className="rounded-full py-6 px-12 bg-grey-50" disableSpread style={{ gap: wp(4) }}>
        <Row disableSpread>
          <CircledIcon className="bg-blue-500 p-4">
            <DashboardIcon size={wp(12)} color={colors.white} />
          </CircledIcon>
          <CircledIcon className="bg-grey-25 p-4 -ml-8">
            <CustomImage
              imageProps={{
                source: require('@assets/images/fire-emoji.png'),
                contentFit: 'contain',
              }}
              className="w-12 h-12"
            />
          </CircledIcon>
        </Row>
        <BaseText fontSize={14} className="text-grey-400">
          32
        </BaseText>
      </Row>
      {/* <Pressable>
        <CircledIcon className="bg-grey-50 py-6 px-12">
          <CheckCircleIcon size={wp(18)} color={colors.grey[300]} />
          <BaseText fontSize={16} weight='medium' className="text-grey-300 ml-5">Done</BaseText>
        </CircledIcon>
      </Pressable> */}
      <SelectEntriesButton isSelectionActive={isSelectionActive} onPressSelectionActive={onPressSelectionActive} />
    </Row>
  );
};

export default HomeHeader;

const SelectEntriesButton = ({
  isSelectionActive,
  onPressSelectionActive,
}: {
  isSelectionActive: SharedValue<boolean>;
  onPressSelectionActive: () => void;
}) => {
  const animatedValue = useDerivedValue(() => {
    return isSelectionActive?.value
      ? withSpring(1, { damping: 33, stiffness: 400, velocity: 8 })
      : withSpring(0, { damping: 23, stiffness: 400, velocity: 8 });
  }, []);

  const textStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      transform: [
        {
          translateX: interpolate(animatedValue.value, [0, 1], [40, 30]),
        },
      ],
    };
  }, []);

  const containerStyle = useAnimatedStyle(() => {
    return {
      width: interpolate(animatedValue.value, [0, 1], [42, 82]),
    };
  }, []);

  const iconType = useDelayedProperty(
    isSelectionActive, 
    'Bold' as const, 
    'Linear' as const, 
    0,
    0
  );

  return (
    <Pressable onPress={onPressSelectionActive}>
      <Animated.View
        style={containerStyle}
        className="flex-row py-6 px-12 rounded-full items-center justify-start bg-grey-50 overflow-hidden">
        <TickCircle variant={iconType} size={wp(18)} color={colors.grey[300]} />
        <Animated.View style={textStyle}>
          <BaseText fontSize={14} weight="medium" className="text-grey-300 ml-5">
            Done
          </BaseText>
        </Animated.View>
      </Animated.View>
    </Pressable>
  );
};

