import React, { Children, ReactNode } from 'react';
import { View } from 'react-native';
import CustomImage from '@components/ui/others/custom-image';
import { BaseText } from '@components/ui/base';
import dayjs from 'dayjs';
import { cx } from '@utils/functions';
import Row from '@components/ui/layout/row';
import { CalenderHeartIcon } from '@components/ui/others/icons';
import { hp, wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import Button from '@components/ui/buttons/button';
import HighlightedText from '@components/ui/others/highlighted-text';
import { BlurView } from 'expo-blur';
import SubscribeModal from '@components/ui/modals/subscribe-modal';
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';

interface HomeEmptyStateProps {}

const HomeEmptyState = ({}: HomeEmptyStateProps) => {
  const [isSubscribeModalVisible, setIsSubscribeModalVisible] = useState(false);
  const navigation = useNavigation();

  return (
    <View className="flex-1 items-start justify-end px-30 pb-[130px]">
      <View
        style={{
          shadowColor: '#000000',
          shadowOffset: { width: 0, height: 9 },
          shadowOpacity: 0.21,
          shadowRadius: 10.5,
        }}>
        <View
          style={{
            shadowColor: '#000000',
            shadowOffset: { width: -3.6, height: 3.6 },
            shadowOpacity: 0.08,
            shadowRadius: 5.5,
          }}>
          <View
            style={{
              shadowColor: '#000000',
              shadowOffset: { width: -1.82, height: 1.82 },
              shadowOpacity: 0.1,
              shadowRadius: 1.5,
            }}>
            <View
              style={{
                shadowColor: '#000000',
                shadowOffset: { width: -0.91, height: 0.91 },
                shadowOpacity: 0.12,
                shadowRadius: 0.005,
              }}>
              <CustomImage
                imageProps={{ source: require('@assets/images/logo-rounded.png'), contentFit: 'contain' }}
                className="w-52 h-52"
              />
            </View>
          </View>
        </View>
      </View>
      <View className="mt-8">
        <BaseText fontSize={16} weight="medium" className="text-grey-300" lineHeight={22}>
          It's{' '}
          <BaseText fontSize={16} weight="bold" className="text-grey-600" lineHeight={22}>
            {dayjs().format('dddd')},
          </BaseText>
        </BaseText>
        <Row disableSpread style={{ gap: wp(3) }}>
          <CalenderHeartIcon size={wp(18)} color={colors.grey[900]} />
          <BaseText fontSize={16} weight="medium" className="text-grey-600" lineHeight={22}>
            {dayjs().format('D of MMMM YYYY')}{' '}
            <BaseText fontSize={16} className="text-grey-300" lineHeight={22}>
              at{' '}
            </BaseText>
            {dayjs().format('hh:mm A')}
          </BaseText>
        </Row>
        <View className="mt-8">
          <HighlightedText
            text="*Begin* Journaling today *and* express yourself *in* everyway possible."
          />
        </View>
        <View className="mt-8">
          <Button text="Write your thoughts" className="self-start" onPress={() => navigation.navigate('WriteJournalScreen')} />
        </View>
      </View>
      <SubscribeModal isVisible={isSubscribeModalVisible} closeModal={() => setIsSubscribeModalVisible(false)} />
    </View>
  );
};

export default HomeEmptyState;
const BlurContainer = ({children}: {children: ReactNode}) => {
  return (
    <BlurView intensity={0}>
      {children}
    </BlurView>
  );
};
