import { useState } from "react";
import { runOnJS, SharedValue, useAnimatedReaction } from "react-native-reanimated";

 const useDelayedProperty = <T>(
  sharedValue: SharedValue<boolean>,
  trueValue: T,
  falseValue: T,
  delayWhenTrue: number = 0,
  delayWhenFalse: number = 0
) => {
  const [property, setProperty] = useState<T>(sharedValue?.value ? trueValue : falseValue);

  // Define these outside the worklet (in JS scope)
const callTrueSetter = () => {
  setProperty(trueValue);
};

const callFalseSetter = () => {
  setProperty(falseValue);
};

useAnimatedReaction(
  () => sharedValue.value,
  (current, previous) => {
    if (current && !previous) {
      // Became true
      if (delayWhenTrue > 0) {
        runOnJS(setTimeout)(callTrueSetter, delayWhenTrue);
      } else {
        runOnJS(callTrueSetter)();
      }
    } else if (!current && previous) {
      // Became false
      if (delayWhenFalse > 0) {
        runOnJS(setTimeout)(callFalseSetter, delayWhenFalse);
      } else {
        runOnJS(callFalseSetter)();
      }
    }
  }
);


  return property;
};

export default useDelayedProperty;
