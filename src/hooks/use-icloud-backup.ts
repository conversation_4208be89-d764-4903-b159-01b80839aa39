import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { CloudStorage, useIsCloudAvailable } from 'react-native-cloud-storage';
import { useJournal } from '@hooks/use-journal-db-service';
import { JournalEntry } from '@services/db/schema';
import { useAppConfig } from '@hooks/use-app-config';

interface BackupData {
  entries: JournalEntry[];
  timestamp: string;
  version: string;
}

interface UseICloudBackup {
  isCloudAvailable: boolean;
  isBackupEnabled: boolean;
  isLoading: boolean;
  lastBackupDate: string | null;
  enableBackup: () => Promise<void>;
  disableBackup: () => Promise<void>;
  createBackup: () => Promise<boolean>;
  restoreFromBackup: () => Promise<boolean>;
  checkForBackup: () => Promise<boolean>;
  autoBackup: () => Promise<void>;
}

const BACKUP_FILE_PATH = '/journal-backup.json';
const APP_VERSION = '1.0.0';

export const useICloudBackup = (): UseICloudBackup => {
  const cloudAvailable = useIsCloudAvailable();
  const { entries, loadEntries } = useJournal();
  const { config: appConfig, updateICloudBackup, isInitialized: isAppConfigInitialized } = useAppConfig();

  const [isLoading, setIsLoading] = useState(false);

  // Get backup settings from app config
  const isBackupEnabled = appConfig?.icloud_backup_enable ?? false;
  const lastBackupDate = appConfig?.last_launch_date ?? null;

  // No initialization needed - data comes from app config

  const createBackup = useCallback(async (): Promise<boolean> => {
    if (!cloudAvailable || !isBackupEnabled) {
      return false;
    }

    try {
      setIsLoading(true);

      const backupData: BackupData = {
        entries,
        timestamp: new Date().toISOString(),
        version: APP_VERSION,
      };

      await CloudStorage.writeFile(
        BACKUP_FILE_PATH,
        JSON.stringify(backupData, null, 2)
      );

      console.log('Backup created successfully');
      return true;
    } catch (error) {
      console.error('Error creating backup:', error);
      Alert.alert('Backup Failed', 'Failed to create backup to iCloud.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [cloudAvailable, isBackupEnabled, entries]);

  const enableBackup = useCallback(async () => {
    if (!cloudAvailable) {
      Alert.alert('iCloud Unavailable', 'iCloud is not available on this device.');
      return;
    }

    if (!isAppConfigInitialized) {
      // Alert.alert('Error', 'App configuration not ready. Please try again.');
      return;
    }

    try {
      setIsLoading(true);
      await updateICloudBackup(true);

      // Create initial backup
      await createBackup();

      Alert.alert('Backup Enabled', 'Your journal will now be automatically backed up to iCloud.');
    } catch (error) {
      console.error('Error enabling backup:', error);
      Alert.alert('Error', 'Failed to enable iCloud backup.');
    } finally {
      setIsLoading(false);
    }
  }, [cloudAvailable, isAppConfigInitialized, updateICloudBackup, createBackup]);

  const disableBackup = useCallback(async () => {
    if (!isAppConfigInitialized) {
      Alert.alert('Error', 'App configuration not ready. Please try again.');
      return;
    }

    try {
      await updateICloudBackup(false);
      Alert.alert('Backup Disabled', 'Automatic iCloud backup has been disabled.');
    } catch (error) {
      console.error('Error disabling backup:', error);
      Alert.alert('Error', 'Failed to disable iCloud backup.');
    }
  }, [isAppConfigInitialized, updateICloudBackup]);

  const autoBackup = useCallback(async () => {
    if (!isBackupEnabled || !cloudAvailable) {
      return;
    }

    // Only auto-backup if it's been more than 1 hour since last backup
    if (lastBackupDate) {
      const lastBackup = new Date(lastBackupDate);
      const now = new Date();
      const hoursSinceLastBackup = (now.getTime() - lastBackup.getTime()) / (1000 * 60 * 60);

      if (hoursSinceLastBackup < 1) {
        return;
      }
    }

    await createBackup();
  }, [isBackupEnabled, cloudAvailable, lastBackupDate, createBackup]);

  // Auto backup when entries change (if enabled)
  useEffect(() => {
    if (isBackupEnabled && entries.length > 0) {
      autoBackup();
    }
  }, [entries, isBackupEnabled, autoBackup]);

  const checkForBackup = useCallback(async (): Promise<boolean> => {
    if (!cloudAvailable) {
      return false;
    }

    try {
      const exists = await CloudStorage.exists(BACKUP_FILE_PATH);
      return exists;
    } catch (error) {
      console.error('Error checking for backup:', error);
      return false;
    }
  }, [cloudAvailable]);

  const restoreFromBackup = useCallback(async (): Promise<boolean> => {
    if (!cloudAvailable) {
      Alert.alert('iCloud Unavailable', 'iCloud is not available on this device.');
      return false;
    }

    try {
      setIsLoading(true);
      
      const backupExists = await checkForBackup();
      if (!backupExists) {
        Alert.alert('No Backup Found', 'No backup was found in iCloud.');
        return false;
      }

      const backupContent = await CloudStorage.readFile(BACKUP_FILE_PATH);
      const backupData: BackupData = JSON.parse(backupContent);

      // Validate backup data
      if (!backupData.entries || !Array.isArray(backupData.entries)) {
        throw new Error('Invalid backup data format');
      }

      // Show confirmation dialog
      return new Promise((resolve) => {
        Alert.alert(
          'Restore from Backup',
          `Found backup from ${new Date(backupData.timestamp).toLocaleDateString()}. This will replace your current journal entries. Continue?`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: 'Restore',
              style: 'destructive',
              onPress: async () => {
                try {
                  // Here you would implement the restore logic
                  // This might involve clearing current entries and adding backup entries
                  // You'll need to implement this based on your database service
                  
                  console.log(`Restoring ${backupData.entries.length} entries from backup`);
                  
                  // Reload entries after restore
                  await loadEntries();
                  
                  Alert.alert('Restore Complete', 'Your journal has been restored from iCloud backup.');
                  resolve(true);
                } catch (error) {
                  console.error('Error during restore:', error);
                  Alert.alert('Restore Failed', 'Failed to restore from backup.');
                  resolve(false);
                }
              },
            },
          ]
        );
      });
    } catch (error) {
      console.error('Error restoring backup:', error);
      Alert.alert('Restore Failed', 'Failed to restore from iCloud backup.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [cloudAvailable, loadEntries, checkForBackup]);

  return {
    isCloudAvailable: cloudAvailable,
    isBackupEnabled,
    isLoading,
    lastBackupDate,
    enableBackup,
    disableBackup,
    createBackup,
    restoreFromBackup,
    checkForBackup,
    autoBackup,
  };
};