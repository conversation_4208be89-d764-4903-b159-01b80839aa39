import { useState, useEffect, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import { CloudStorage, CloudStorageProvider, useIsCloudAvailable } from 'react-native-cloud-storage';
import { useJournal } from '@hooks/use-journal-db-service';
import { JournalEntry } from '@services/db/schema';
import { MMKV } from 'react-native-mmkv';

interface BackupData {
  entries: JournalEntry[];
  timestamp: string;
  version: string;
}

interface UseICloudBackup {
  isCloudAvailable: boolean;
  isBackupEnabled: boolean;
  isLoading: boolean;
  lastBackupDate: string | null;
  enableBackup: () => Promise<void>;
  disableBackup: () => Promise<void>;
  createBackup: () => Promise<boolean>;
  restoreFromBackup: () => Promise<boolean>;
  checkForBackup: () => Promise<boolean>;
  autoBackup: () => Promise<void>;
}

const BACKUP_FILE_PATH = '/journal-backup.json';
const BACKUP_ENABLED_KEY = 'icloud_backup_enabled';
const LAST_BACKUP_KEY = 'last_backup_date';
const APP_VERSION = '1.0.0';

// Initialize MMKV storage
const storage = new MMKV();

export const useICloudBackup = (): UseICloudBackup => {
  const cloudAvailable = useIsCloudAvailable();
  const { entries, addEntry, loadEntries } = useJournal();
  
  const [isBackupEnabled, setIsBackupEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [lastBackupDate, setLastBackupDate] = useState<string | null>(null);

  // Initialize backup settings
  useEffect(() => {
    const initializeSettings = async () => {
      try {
        const enabled = storage.getBoolean(BACKUP_ENABLED_KEY) ?? false;
        const lastBackup = storage.getString(LAST_BACKUP_KEY) ?? null;
        
        setIsBackupEnabled(enabled);
        setLastBackupDate(lastBackup);
      } catch (error) {
        console.error('Error initializing backup settings:', error);
      }
    };

    initializeSettings();
  }, []);

  // Auto backup when entries change (if enabled)
  useEffect(() => {
    if (isBackupEnabled && entries.length > 0) {
      autoBackup();
    }
  }, [entries, isBackupEnabled]);

  const enableBackup = useCallback(async () => {
    if (!cloudAvailable) {
      Alert.alert('iCloud Unavailable', 'iCloud is not available on this device.');
      return;
    }

    try {
      setIsLoading(true);
      storage.set(BACKUP_ENABLED_KEY, true);
      setIsBackupEnabled(true);
      
      // Create initial backup
      await createBackup();
      
      Alert.alert('Backup Enabled', 'Your journal will now be automatically backed up to iCloud.');
    } catch (error) {
      console.error('Error enabling backup:', error);
      Alert.alert('Error', 'Failed to enable iCloud backup.');
    } finally {
      setIsLoading(false);
    }
  }, [cloudAvailable]);

  const disableBackup = useCallback(async () => {
    try {
      storage.set(BACKUP_ENABLED_KEY, false);
      setIsBackupEnabled(false);
      Alert.alert('Backup Disabled', 'Automatic iCloud backup has been disabled.');
    } catch (error) {
      console.error('Error disabling backup:', error);
    }
  }, []);

  const createBackup = useCallback(async (): Promise<boolean> => {
    if (!cloudAvailable || !isBackupEnabled) {
      return false;
    }

    try {
      setIsLoading(true);
      
      const backupData: BackupData = {
        entries,
        timestamp: new Date().toISOString(),
        version: APP_VERSION,
      };

      await CloudStorage.writeFile(
        BACKUP_FILE_PATH,
        JSON.stringify(backupData, null, 2)
      );

      const currentDate = new Date().toISOString();
      storage.set(LAST_BACKUP_KEY, currentDate);
      setLastBackupDate(currentDate);

      console.log('Backup created successfully');
      return true;
    } catch (error) {
      console.error('Error creating backup:', error);
      Alert.alert('Backup Failed', 'Failed to create backup to iCloud.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [cloudAvailable, isBackupEnabled, entries]);

  const checkForBackup = useCallback(async (): Promise<boolean> => {
    if (!cloudAvailable) {
      return false;
    }

    try {
      const exists = await CloudStorage.exists(BACKUP_FILE_PATH);
      return exists;
    } catch (error) {
      console.error('Error checking for backup:', error);
      return false;
    }
  }, [cloudAvailable]);

  const restoreFromBackup = useCallback(async (): Promise<boolean> => {
    if (!cloudAvailable) {
      Alert.alert('iCloud Unavailable', 'iCloud is not available on this device.');
      return false;
    }

    try {
      setIsLoading(true);
      
      const backupExists = await checkForBackup();
      if (!backupExists) {
        Alert.alert('No Backup Found', 'No backup was found in iCloud.');
        return false;
      }

      const backupContent = await CloudStorage.readFile(BACKUP_FILE_PATH);
      const backupData: BackupData = JSON.parse(backupContent);

      // Validate backup data
      if (!backupData.entries || !Array.isArray(backupData.entries)) {
        throw new Error('Invalid backup data format');
      }

      // Show confirmation dialog
      return new Promise((resolve) => {
        Alert.alert(
          'Restore from Backup',
          `Found backup from ${new Date(backupData.timestamp).toLocaleDateString()}. This will replace your current journal entries. Continue?`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: 'Restore',
              style: 'destructive',
              onPress: async () => {
                try {
                  // Here you would implement the restore logic
                  // This might involve clearing current entries and adding backup entries
                  // You'll need to implement this based on your database service
                  
                  console.log(`Restoring ${backupData.entries.length} entries from backup`);
                  
                  // Reload entries after restore
                  await loadEntries();
                  
                  Alert.alert('Restore Complete', 'Your journal has been restored from iCloud backup.');
                  resolve(true);
                } catch (error) {
                  console.error('Error during restore:', error);
                  Alert.alert('Restore Failed', 'Failed to restore from backup.');
                  resolve(false);
                }
              },
            },
          ]
        );
      });
    } catch (error) {
      console.error('Error restoring backup:', error);
      Alert.alert('Restore Failed', 'Failed to restore from iCloud backup.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [cloudAvailable, loadEntries]);

  const autoBackup = useCallback(async () => {
    if (!isBackupEnabled || !cloudAvailable) {
      return;
    }

    // Only auto-backup if it's been more than 1 hour since last backup
    if (lastBackupDate) {
      const lastBackup = new Date(lastBackupDate);
      const now = new Date();
      const hoursSinceLastBackup = (now.getTime() - lastBackup.getTime()) / (1000 * 60 * 60);
      
      if (hoursSinceLastBackup < 1) {
        return;
      }
    }

    await createBackup();
  }, [isBackupEnabled, cloudAvailable, lastBackupDate, createBackup]);

  return {
    isCloudAvailable: cloudAvailable,
    isBackupEnabled,
    isLoading,
    lastBackupDate,
    enableBackup,
    disableBackup,
    createBackup,
    restoreFromBackup,
    checkForBackup,
    autoBackup,
  };
};