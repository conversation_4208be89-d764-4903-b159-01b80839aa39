import { useState, useEffect, useCallback, useMemo } from 'react';
import { Alert } from 'react-native';
import { and, gte, lt } from 'drizzle-orm';
import {
  createJournalEntry,
  getAllJournalEntries,
  getJournalEntryById,
  updateJournalEntry,
  deleteJournalEntry,
  searchJournalEntries,
  getJournalEntriesByMood,
  getAllTodayEntries,
  getBookmarkedJournalEntries,
  deleteJournalEntries,
} from '@services/db/journal';
import { InsertJournalEntry, journalEntries, JournalEntry, SelectJournalEntry } from '@services/db/schema';
import { initTable } from '@services/db/migration';
import { isMood, MOOD_VARIANT } from 'src/@types/app-interface';
import { useLiveQuery } from 'drizzle-orm/expo-sqlite';
import { getDB } from '@services/db/db';

// Get today's date boundaries
const today = new Date();
today.setHours(0, 0, 0, 0); // Start of today (midnight)
const todayISO = today.toISOString();

const tomorrow = new Date(today);
tomorrow.setDate(tomorrow.getDate() + 1); // Start of tomorrow
const tomorrowISO = tomorrow.toISOString();

interface JournalUpdateInput {
  mood?: MOOD_VARIANT;
  title?: string;
  content?: string;
  images?: string[];
}
const db = getDB();

export function useJournal() {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDbInitialized, setIsDbInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const todayEntriesFromDb = useLiveQuery(
    db
      .select()
      .from(journalEntries)
      .where(and(gte(journalEntries.created_date, todayISO), lt(journalEntries.created_date, tomorrowISO))),
  );

  const todayEntries = useMemo(() => {
    if (todayEntriesFromDb?.data) {
      const formattedEntries = todayEntriesFromDb?.data.map(row => {
        if (!isMood(row.mood)) {
          throw new Error(`Invalid mood found in DB: ${row.mood}`);
        }

        return {
          ...row,
          mood: row.mood as MOOD_VARIANT,
          images: Array.isArray(row.images) ? row.images : [],
        };
      });
      return formattedEntries;
    }

    return [];
  }, [todayEntriesFromDb]);

  // Initialize DB
  useEffect(() => {
    (async () => {
      try {
        setIsLoading(true);
        const success = await initTable();
        setIsDbInitialized(success);
        if (!success) setError('Failed to initialize the database');
      } catch (err) {
        setError(`Initialization error: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setIsLoading(false);
      }
    })();
  }, []);

  const loadEntries = useCallback(async () => {
    if (!isDbInitialized) return;
    try {
      setIsLoading(true);
      const result = await getAllJournalEntries();
      const formattedEntries = transformEntries(result);
      setEntries(formattedEntries);
    } catch (err) {
      setError(`Load failed: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  }, [isDbInitialized]);

  const entriesByDay = useMemo(() => {
    const grouped = entries.reduce((groupedEntries, entry) => {
      const date = entry.entry_date.split('T')[0];
      if (!isToday(new Date(date))) {
        if (!groupedEntries[date]) {
          groupedEntries[date] = [];
        }

        groupedEntries[date].push(entry);
      }

      return groupedEntries;
    }, {} as { [date: string]: JournalEntry[] });

    console.log(grouped);

    const entriesByDayFlat = Object.entries(grouped)
      .map(([dateStr, entries]) => ({
        date: new Date(dateStr),
        entries,
      }))
      .sort((a, b) => b.date.getTime() - a.date.getTime());

      console.log(entriesByDayFlat);

    return entriesByDayFlat;
  }, [entries]);

  const entriesByMonth = useMemo(() => {
  const grouped = entries.reduce((groupedEntries, entry) => {
    const date = entry.entry_date.split('T')[0];
    if (!isToday(new Date(date))) {
      const dateObj = new Date(date);
      const monthKey = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}`;
      
      if (!groupedEntries[monthKey]) {
        groupedEntries[monthKey] = {};
      }
      
      if (!groupedEntries[monthKey][date]) {
        groupedEntries[monthKey][date] = [];
      }
      
      groupedEntries[monthKey][date].push(entry);
    }
    return groupedEntries;
  }, {} as { [month: string]: { [date: string]: JournalEntry[] } });

  console.log(grouped);

  const entriesByMonthFlat = Object.entries(grouped)
    .map(([monthStr, dateGroups]) => ({
      month: monthStr,
      monthDate: new Date(monthStr + '-01'), // For sorting purposes
      entriesByDay: Object.entries(dateGroups)
        .map(([dateStr, entries]) => ({
          date: new Date(dateStr),
          entries,
        }))
        .sort((a, b) => b.date.getTime() - a.date.getTime()) // Sort dates within month (newest first)
    }))
    .sort((a, b) => b.monthDate.getTime() - a.monthDate.getTime()); // Sort months (newest first)

  console.log(entriesByMonthFlat);
  return entriesByMonthFlat;
}, [entries]);

  const addEntry = useCallback(
    async (entry: InsertJournalEntry) => {
      if (!isDbInitialized) return null;
      try {
        setIsLoading(true);
        const newEntry = await createJournalEntry(entry);

        const entryFormatted = {
          ...newEntry,
          mood: newEntry.mood as MOOD_VARIANT,
          images: Array.isArray(newEntry.images) ? newEntry.images : [],
        };
        // setEntries(prev => [entryFormatted, ...prev]);
        return entryFormatted.id;
      } catch (err) {
        console.log(err);
        setError(`Create failed: ${err instanceof Error ? err.message : String(err)}`);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [isDbInitialized],
  );

  const getEntry = useCallback(
    async (id: string) => {
      if (!isDbInitialized) return null;
      try {
        setIsLoading(true);
        const entry = await getJournalEntryById(id);
        if (entry) {
          const entryFormatted = {
            ...entry,
            mood: entry.mood as MOOD_VARIANT,
            images: Array.isArray(entry.images) ? entry.images : [],
          };
          setCurrentEntry(entryFormatted);
        }
        return entry || null;
      } catch (err) {
        setError(`Fetch failed: ${err instanceof Error ? err.message : String(err)}`);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [isDbInitialized],
  );

  const editEntry = useCallback(
    async (id: string, updates: InsertJournalEntry) => {
      if (!isDbInitialized) return null;
      try {
        setIsLoading(true);
        const updated = await updateJournalEntry(id, updates);
        // if (updated) {
        //   setEntries(prev => prev.map(e => (e.id === id ? updated : e)));
        //   if (currentEntry?.id === id) setCurrentEntry(updated);
        // }
        return updated || null;
      } catch (err) {
        setError(`Update failed: ${err instanceof Error ? err.message : String(err)}`);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [isDbInitialized, currentEntry],
  );

// Updated React hook function to handle multiple entries
const removeEntries = useCallback(
  async (ids: string[]) => {
    if (!isDbInitialized || ids.length === 0) return false;
    try {
      setIsLoading(true);
      const success = await deleteJournalEntries(ids);
      if (success) {
        setEntries(prev => prev.filter(e => !ids.includes(e.id)));
        // Clear current entry if it was one of the deleted entries
        if (currentEntry && ids.includes(currentEntry.id)) {
          setCurrentEntry(null);
        }
      }
      return success;
    } catch (err) {
      setError(`Delete failed: ${err instanceof Error ? err.message : String(err)}`);
      return false;
    } finally {
      setIsLoading(false);
    }
  },
  [isDbInitialized, currentEntry],
);

// Keep the original single entry removal for backward compatibility
const removeEntry = useCallback(
  async (id: string) => {
    return await removeEntries([id]);
  },
  [removeEntries],
);

  const searchEntries = useCallback(
    async (query: string) => {
      if (!isDbInitialized) return [];
      try {
        setIsLoading(true);
        const searchResult = await searchJournalEntries(query);
        return transformEntries(searchResult);
      } catch (err) {
        setError(`Search failed: ${err instanceof Error ? err.message : String(err)}`);
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [isDbInitialized],
  );

  const filterByMood = useCallback(
    async (mood: MOOD_VARIANT) => {
      if (!isDbInitialized) return [];
      try {
        setIsLoading(true);
        return await getJournalEntriesByMood(mood);
      } catch (err) {
        setError(`Filter failed: ${err instanceof Error ? err.message : String(err)}`);
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [isDbInitialized],
  );

  const confirmAndDeleteEntry = useCallback(
    (id: string) => {
      return new Promise<boolean>(resolve => {
        Alert.alert(
          'Delete Journal Entry',
          'Are you sure you want to delete this entry? This action cannot be undone.',
          [
            { text: 'Cancel', style: 'cancel', onPress: () => resolve(false) },
            {
              text: 'Delete',
              style: 'destructive',
              onPress: async () => resolve(await removeEntry(id)),
            },
          ],
        );
      });
    },
    [removeEntry],
  );

  const confirmAndDeleteEntries = useCallback(
    (ids: string[]) => {
      return new Promise<boolean>(resolve => {
        Alert.alert(
          'Delete Journal Entry',
          'Are you sure you want to delete the selected entries? This action cannot be undone.',
          [
            { text: 'Cancel', style: 'cancel', onPress: () => resolve(false) },
            {
              text: 'Delete',
              style: 'destructive',
              onPress: async () => resolve(await removeEntries(ids)),
            },
          ],
        );
      });
    },
    [removeEntries],
  );

  const getBookmarkedEntries = useCallback(async () => {
    if (!isDbInitialized) return [];
    try {
      setIsLoading(true);
      const bookmarks = await getBookmarkedJournalEntries();

      const formattedEntries = bookmarks.map(row => {
        if (!isMood(row.mood)) {
          throw new Error(`Invalid mood found in DB: ${row.mood}`);
        }

        return {
          ...row,
          mood: row.mood as MOOD_VARIANT,
          images: Array.isArray(row.images) ? row.images : [],
        };
      });
      return transformEntries(bookmarks);
    } catch (err) {
      setError(`Filter failed: ${err instanceof Error ? err.message : String(err)}`);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [isDbInitialized]);

  return {
    entries,
    todayEntries: todayEntries ?? [],
    currentEntry,
    isLoading,
    isDbInitialized,
    entriesByDay,
    entriesByMonth,
    error,
    clearError: () => setError(null),

    // Core operations
    loadEntries,
    addEntry,
    getEntry,
    editEntry,
    removeEntry,
    confirmAndDeleteEntry,
    confirmAndDeleteEntries,
    searchEntries,
    removeEntries,
    filterByMood,
    getBookmarkedEntries,
  };
}

function isToday(date: Date) {
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
}

// const loadTodayEntries = useCallback(async () => {
//   if (!isDbInitialized) return;
//   try {
//     setIsLoading(true);
//     const result = await getAllTodayEntries();
//     const formattedEntries = result.map(row => {
//       if (!isMood(row.mood)) {
//         throw new Error(`Invalid mood found in DB: ${row.mood}`);
//       }

//       return {
//         ...row,
//         mood: row.mood as MOOD_VARIANT,
//         images: Array.isArray(row.images) ? row.images : [],
//       };
//     });
//     // setTodayEntries(formattedEntries);
//   } catch (err) {
//     setError(`Load failed: ${err instanceof Error ? err.message : String(err)}`);
//   } finally {
//     setIsLoading(false);
//   }
// }, [isDbInitialized]);

const transformEntries = (entries: SelectJournalEntry[]) => {
  return entries.map(entry => ({
    ...entry,
    mood: entry.mood as MOOD_VARIANT,
    images: Array.isArray(entry.images) ? entry.images : [],
  }));
};
