import { useState, useCallback, useRef, useEffect } from 'react';
import MapboxService, { SearchboxCategory, SearchboxOptions, SearchResult } from 'src/services/mapbox';

export interface LocationSearchResult {
  latitude: number;
  longitude: number;
  address: string;
  name: string;
  details: SearchResult['details'];
}

export function useLocationSearch(debounceMs = 500) {
  const [isSearching, setIsSearching] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const searchPlaces = useCallback(async (
    searchText: string,
    options?: SearchboxOptions
  ) => {
    if (!searchText.trim()) return [];

    try {
      const results = await MapboxService.searchPlaces(searchText, options);

      return results.map(result => ({
        latitude: result.coordinates.latitude,
        longitude: result.coordinates.longitude,
        address: result.fullAddress,
        name: result.name,
        details: result.details
      }));
    } catch (error) {
      console.error('Error searching places:', error);
      throw error;
    }
  }, []);

  const debouncedSearch = useCallback((
    searchText: string,
    options?: SearchboxOptions & {
      onSuccess?: (results: LocationSearchResult[]) => void;
      onError?: (error: Error) => void;
    }
  ) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setIsSearching(true);

    timeoutRef.current = setTimeout(async () => {
      try {
        const { onSuccess, onError, ...searchOptions } = options || {};
        const results = await searchPlaces(searchText, searchOptions);
        onSuccess?.(results);
      } catch (error) {
        const err = error instanceof Error ? error : new Error('Failed to search places');
        options?.onError?.(err);
      } finally {
        setIsSearching(false);
      }
    }, debounceMs);
  }, [searchPlaces, debounceMs]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const reverseGeocode = useCallback(async (latitude: number, longitude: number) => {
    try {
      const result = await MapboxService.reverseGeocode(latitude, longitude);
      return result;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      throw error;
    }
  }, []);

  return {
    searchPlaces: debouncedSearch,
    isSearching,
    reverseGeocode
  };
}
