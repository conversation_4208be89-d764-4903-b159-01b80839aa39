import { useState, useEffect, useCallback } from 'react';
import { getOrCreateAppConfig, updateICloudBackupSetting } from '@services/db/app-config';
import { SelectAppConfig } from '@services/db/schema';

interface UseAppConfig {
  config: SelectAppConfig | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
  updateICloudBackup: (enabled: boolean) => Promise<void>;
  refreshConfig: () => Promise<void>;
}

export const useAppConfig = (): UseAppConfig => {
  const [config, setConfig] = useState<SelectAppConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const initializeConfig = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const appConfig = await getOrCreateAppConfig();
      setConfig(appConfig);
      setIsInitialized(true);
      
      console.log('App config initialized:', {
        user_id: appConfig.user_id,
        device_id: appConfig.device_id,
        device_name: appConfig.device_name,
        first_launch: appConfig.first_launch_date,
        last_launch: appConfig.last_launch_date,
        icloud_backup: appConfig.icloud_backup_enable
      });
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize app config';
      setError(errorMessage);
      console.error('App config initialization error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateICloudBackup = useCallback(async (enabled: boolean) => {
    if (!config) {
      throw new Error('App config not initialized');
    }

    try {
      setIsLoading(true);
      const updatedConfig = await updateICloudBackupSetting(config.id, enabled);
      if (updatedConfig) {
        setConfig(updatedConfig);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update iCloud backup setting';
      setError(errorMessage);
      console.error('iCloud backup update error:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [config]);

  const refreshConfig = useCallback(async () => {
    await initializeConfig();
  }, [initializeConfig]);

  // Initialize on mount
  useEffect(() => {
    initializeConfig();
  }, [initializeConfig]);

  return {
    config,
    isLoading,
    error,
    isInitialized,
    updateICloudBackup,
    refreshConfig,
  };
};
