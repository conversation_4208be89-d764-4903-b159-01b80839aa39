import { useState } from 'react';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import * as Crypto from 'expo-crypto';

// Define cache directory
// const IMAGE_CACHE_DIRECTORY = `${FileSystem.cacheDirectory}image-cache/`;
const IMAGE_CACHE_DIRECTORY = `${FileSystem.documentDirectory}image-cache/`;

// Types
interface UseImagePickerOptions {
  allowsMultipleSelection?: boolean;
  selectionLimit?: number;
  aspect?: [number, number];
  quality?: number;
  // New caching options
  enableCaching?: boolean;
  compressionQuality?: number;
  maxWidth?: number;
  maxHeight?: number;
}

interface CachedAsset {
  uri: string;
  width?: number;
  height?: number;
  fileName: string;
  originalUri: string;
  metadataUri?: string;
  metadata?: any;
}

interface ImagePickerResult extends Omit<ImagePicker.ImagePickerResult, 'assets'> {
  assets: (ImagePicker.ImagePickerAsset | CachedAsset)[];
}

interface UseImagePicker {
  pickImage: () => Promise<ImagePickerResult>;
  launchCamera: () => Promise<ImagePickerResult>;
  isLoading: boolean;
  cacheImages: (assets: ImagePicker.ImagePickerAsset[]) => Promise<CachedAsset[]>;
  getAllCachedImages: () => Promise<CachedAsset[]>;
  deleteCachedImage: (uri: string) => Promise<boolean>;
}

const defaultOptions: UseImagePickerOptions = {
  allowsMultipleSelection: true,
  selectionLimit: 10,
  aspect: [4, 3],
  quality: 1,
  // Default caching options
  enableCaching: true,
  compressionQuality: 0.7,
  maxWidth: 1200,
  maxHeight: 1200,
};

export const useImagePicker = (
  options: UseImagePickerOptions = { allowsMultipleSelection: true, selectionLimit: 10, enableCaching: true },
): UseImagePicker => {
  const [isLoading, setIsLoading] = useState(false);

  const {
    allowsMultipleSelection,
    selectionLimit,
    aspect,
    quality,
    enableCaching,
    compressionQuality,
    maxWidth,
    maxHeight,
  } = {
    ...defaultOptions,
    ...options,
  };

  /**
   * Ensure the cache directory exists
   */
  const ensureCacheDirectoryExists = async (): Promise<void> => {
    const dirInfo = await FileSystem.getInfoAsync(IMAGE_CACHE_DIRECTORY);
    if (!dirInfo.exists) {
      console.log('Creating permanent cache directory...');
      await FileSystem.makeDirectoryAsync(IMAGE_CACHE_DIRECTORY, {
        intermediates: true,
      });
      // Create a .nomedia file on Android to prevent media scanners from indexing these images
      // This won't affect iOS but is harmless there
      // try {
      //   await FileSystem.writeAsStringAsync(
      //     `${IMAGE_CACHE_DIRECTORY}.nomedia`,
      //     ""
      //   );
      // } catch (error) {
      //   // Ignore errors creating .nomedia file
      //   console.log('Note: .nomedia creation optional, error is fine');
      // }
    }
  };

  /**
   * Generate a unique file name for caching
   */
  const generateUniqueFileName = async (uri: string): Promise<string> => {
    // Create a hash from the original URI plus timestamp to ensure uniqueness
    const timestamp = Date.now().toString();
    const data = uri + timestamp;
    const hash = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, data);
    return hash.substring(0, 15) + '.jpg';
  };

  /**
   * Compress an image
   */
  const compressImage = async (
    uri: string,
    options: { quality?: number; width?: number; height?: number } = {},
  ): Promise<ImageManipulator.ImageResult> => {
    try {
      const { quality = compressionQuality, width = maxWidth } = options;

      const manipulatorOptions = {
        compress: quality,
        format: ImageManipulator.SaveFormat.JPEG,
      };

      // Only add resize if either width or height is provided
      const resize = [];
      if (width) {
        resize.push({
          resize: {
            width,
          },
        });
      }

      const manipulateResult = await ImageManipulator.manipulateAsync(
        uri,
        resize.length > 0 ? resize : [],
        manipulatorOptions,
      );

      return manipulateResult;
    } catch (error) {
      console.error('Error compressing image:', error);
      // If compression fails, return the original URI
      return { uri, width: 0, height: 0 };
    }
  };

  /**
   * Cache a single image
   */
  const cacheImage = async (uri: string): Promise<CachedAsset> => {
    try {
      await ensureCacheDirectoryExists();

      const fileName = await generateUniqueFileName(uri);
      const cachedUri = `${IMAGE_CACHE_DIRECTORY}${fileName}`;

      // Copy the file to our permanent cache location
      await FileSystem.copyAsync({
        from: uri,
        to: cachedUri,
      });

      // Create metadata file to store additional info if needed
      const metadataUri = `${cachedUri}.meta`;
      const metadata = {
        originalUri: uri,
        dateCreated: new Date().toISOString(),
      };

      await FileSystem.writeAsStringAsync(metadataUri, JSON.stringify(metadata), {
        encoding: FileSystem.EncodingType.UTF8,
      });

      return {
        uri: cachedUri,
        fileName,
        originalUri: uri,
        metadataUri,
        metadata,
      };
    } catch (error) {
      console.error('Error caching image:', error);
      throw error;
    }
  };

  /**
   * Cache multiple images (for use after selecting multiple images)
   */
  const cacheImages = async (assets: ImagePicker.ImagePickerAsset[]): Promise<CachedAsset[]> => {
    if (!enableCaching || !assets || assets.length === 0) {
      return [];
    }

    setIsLoading(true);
    try {
      const cachedAssets = await Promise.all(
        assets.map(async asset => {
          // First compress the image
          const compressedImage = await compressImage(asset.uri, {
            quality: compressionQuality,
            width: maxWidth,
            height: maxHeight,
          });

          // Then cache the compressed image
          const cachedImage = await cacheImage(compressedImage.uri);

          return {
            ...cachedImage,
            width: compressedImage.width,
            height: compressedImage.height,
          };
        }),
      );

      return cachedAssets;
    } catch (error) {
      console.error('Error caching images:', error);
      Alert.alert('Error', 'Failed to cache images');
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Pick images from gallery with optional caching
   */
  const pickImage = async (): Promise<ImagePickerResult> => {
    setIsLoading(true);
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to add photos.', [{ text: 'OK' }]);
        return { canceled: true, assets: [] };
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: !allowsMultipleSelection, // Only allow editing for single selection
        aspect,
        quality,
        allowsMultipleSelection,
        selectionLimit,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return { canceled: true, assets: [] };
      }

      // If caching is enabled, compress and cache the selected images
      if (enableCaching) {
        const cachedAssets = await cacheImages(result.assets);
        return {
          ...result,
          assets: cachedAssets,
        };
      }

      return result;
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
      return { canceled: true, assets: [] };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Launch camera with optional caching
   */
  const launchCamera = async (): Promise<ImagePickerResult> => {
    setIsLoading(true);
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera permissions to take photos.', [{ text: 'OK' }]);
        return { canceled: true, assets: [] };
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect,
        quality,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return { canceled: true, assets: [] };
      }

      // If caching is enabled, compress and cache the captured image
      if (enableCaching) {
        const cachedAssets = await cacheImages(result.assets);
        return {
          ...result,
          assets: cachedAssets,
        };
      }

      return result;
    } catch (error) {
      console.error('Error launching camera:', error);
      Alert.alert('Error', 'Failed to launch camera');
      return { canceled: true, assets: [] };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get all cached images
   */
  const getAllCachedImages = async (): Promise<CachedAsset[]> => {
    try {
      await ensureCacheDirectoryExists();

      const files = await FileSystem.readDirectoryAsync(IMAGE_CACHE_DIRECTORY);

      // Filter out only JPG files (not metadata files)
      const imageFiles = files.filter(file => file.endsWith('.jpg') && !file.endsWith('.meta'));

      // Create full URIs and get metadata
      const cachedImages = await Promise.all(
        imageFiles.map(async fileName => {
          const uri = `${IMAGE_CACHE_DIRECTORY}${fileName}`;
          const metadataUri = `${uri}.meta`;

          try {
            const metadataString = await FileSystem.readAsStringAsync(metadataUri);
            const metadata = JSON.parse(metadataString);

            return {
              uri,
              fileName,
              originalUri: metadata.originalUri,
              metadata,
            };
          } catch (error) {
            // If metadata can't be read, just return the image URI
            console.log(error);
            return {
              uri,
              fileName,
              originalUri: uri,
            };
          }
        }),
      );

      return cachedImages;
    } catch (error) {
      console.error('Error getting cached images:', error);
      return [];
    }
  };

  /**
   * Delete a cached image
   */
  const deleteCachedImage = async (uri: string): Promise<boolean> => {
    try {
      // Delete the image file
      await FileSystem.deleteAsync(uri, { idempotent: true });

      // Also delete its metadata file if it exists
      const metadataUri = `${uri}.meta`;
      await FileSystem.deleteAsync(metadataUri, { idempotent: true });

      return true;
    } catch (error) {
      console.error('Error deleting cached image:', error);
      return false;
    }
  };

  return {
    pickImage,
    launchCamera,
    isLoading,
    cacheImages,
    getAllCachedImages,
    deleteCachedImage,
  };
};
