import classNames from 'classnames';
import dayjs from 'dayjs';
import { Dimensions } from 'react-native';

export const cx = classNames;


export const formatDate = (date: Date | string, dateFormat?: string) => {
  // const dateString = format(new Date(date), dateFormat ? dateFormat : 'EEE, MMM d yyyy');
  const dateString = dayjs(date).format(dateFormat ?? 'D MMM YYYY, hh:mm A');
  return dateString;
};

export const screenWidth = Dimensions.get('window').width; 

export const delay = async (time: number): Promise<void> => {
  return new Promise(resolve => {
    const id = setTimeout(() => {
      resolve();
      clearTimeout(id);
    }, time);
  });
};