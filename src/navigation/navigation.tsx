import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createStackNavigator } from '@react-navigation/stack';
import HomeScreen from '@screens/home/<USER>';
import SettingsScreen from '@screens/settings/settings';
import DashboardScreen from '@screens/dashboard/dashboard-screen';
import WriteJournalScreen from '@screens/write-journal/write-journal';

// Create the stack navigator
const Stack = createStackNavigator();

const BaseApp = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="HomeScreen"
        screenOptions={{
          headerShown: false,
        }}>
        <Stack.Screen
          name="HomeScreen"
          component={HomeScreen}
          options={{
            cardStyle: {
              borderTopLeftRadius: 30,
              borderTopRightRadius: 30,
              backgroundColor: 'white', // Optional: Set a background color
            },
          }}
        />
        <Stack.Screen name="DashboardScreen" component={DashboardScreen} />
        <Stack.Screen
          options={{
            presentation: 'transparentModal',
            animation: 'fade_from_bottom',
          }}
          name="WriteJournalScreen"
          component={WriteJournalScreen}
        />
        <Stack.Screen
          options={{
            presentation: 'modal',
            cardStyle: {
              borderTopLeftRadius: 30,
              borderTopRightRadius: 30,
              backgroundColor: 'white', // Optional: Set a background color
            },
          }}
          name="Settings"
          component={SettingsStack}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const SettingsStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="SettingsScreen"
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name="SettingsScreen" component={SettingsScreen} />
    </Stack.Navigator>
  );
};

export default BaseApp;
