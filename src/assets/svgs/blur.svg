<svg width="width" height="height" viewBox="0 0 width 133" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_26_9344" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="390" height="133">
<rect width="390" height="133" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_26_9344)">
<foreignObject x="-56.1531" y="-67.1531" width="526.306" height="257.306"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(11.44px);clip-path:url(#bgblur_0_26_9344_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_f_26_9344)" data-figma-bg-blur-radius="22.8839">
<path d="M-30 -41H444V164H-30V-41Z" fill="url(#paint0_radial_26_9344)"/>
</g>
</g>
<defs>
<filter id="filter0_f_26_9344" x="-56.1531" y="-67.1531" width="526.306" height="257.306" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="13.0765" result="effect1_foregroundBlur_26_9344"/>
</filter>
<clipPath id="bgblur_0_26_9344_clip_path" transform="translate(56.1531 67.1531)"><path d="M-30 -41H444V164H-30V-41Z"/>
</clipPath><radialGradient id="paint0_radial_26_9344" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(207 -269.952) rotate(90.6143) scale(412.9 830.586)">
<stop offset="0.744685" stop-color="white" stop-opacity="0"/>
<stop offset="0.873683" stop-color="white"/>
</radialGradient>
</defs>
</svg>
