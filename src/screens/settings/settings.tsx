import { Alert, Switch, View } from 'react-native';
import { BaseText } from '@components/ui/base';
import DashboardLayout from '@components/ui/layout/dashboard-layout';
import BottomTab from '@components/ui/layout/bottom-tab';
import { ScrollView } from 'react-native-gesture-handler';
import useStatusbar from '@hooks/use-statusbar';
import Row from '@components/ui/layout/row';
import CircledIcon from '@components/ui/others/circled-icon';
import {
  BellIcon,
  CancelIcon,
  ChatIcon,
  EmailIcon,
  FileShieldIcon,
  FingerprintIcon,
  ICloudIcon,
  LogoutIcon,
  NotesIcon,
  TrashIcon,
} from '@components/ui/others/icons';
import { wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import Pressable from '@components/ui/base/pressable';
import { useNavigation } from '@react-navigation/native';
import { ReactNode } from 'react';
import { BoxTime, CloudAdd } from 'iconsax-react-native';
import { BlurView } from 'expo-blur';
import { useICloudBackup } from '@hooks/use-icloud-backup';

const SettingsScreen = () => {
  const { setStatusBar } = useStatusbar('light');
  const navigation = useNavigation();
  const {
    isCloudAvailable,
    isBackupEnabled,
    isLoading,
    lastBackupDate,
    enableBackup,
    disableBackup,
    restoreFromBackup,
  } = useICloudBackup();

  const handleBackupToggle = async () => {
    if (!isCloudAvailable) {
      Alert.alert('iCloud Unavailable', 'iCloud is not available on this device.');
      return;
    }
    if (isBackupEnabled) {
      await disableBackup();
    } else {
      await enableBackup();
    }
  };

  const handleRestoreBackup = async () => {
    await restoreFromBackup();
  };

  const settings = [
    {
      title: 'Profile',
      items: [
        // {
        //   title: 'Email',
        //   icon: (
        //     <Icon>
        //       <EmailIcon size={wp(18)} color={colors.grey[700]} />
        //     </Icon>
        //   ),
        //   rightElement: (
        //     <BaseText fontSize={16} weight="medium" className="text-grey-200">
        //       <EMAIL>
        //     </BaseText>
        //   ),
        // },
        {
          title: 'ICloud Backup',
          // description: isCloudAvailable
          //   ? `Store your Data on the cloud.${
          //       lastBackupDate ? ` Last backup: ${new Date(lastBackupDate).toLocaleDateString()}` : ''
          //     }`
          //   : 'iCloud is not available on this device.',
          description: 'Store your Data on the cloud',
          icon: (
            <Icon>
              <ICloudIcon size={wp(18)} color={colors.grey[700]} />
            </Icon>
          ),
          rightElement: (
            <Switch
              value={isBackupEnabled}
              onValueChange={handleBackupToggle}
              // disabled={!isCloudAvailable || isLoading}
              disabled={isLoading}
            />
          ),
        },
        ...(isBackupEnabled
          ? [
              {
                title: 'Restore from Backup',
                description: 'Restore your journal from iCloud backup.',
                icon: (
                  <Icon>
                    <CloudAdd size={wp(18)} color={colors.grey[700]} />
                  </Icon>
                ),
                rightElement: (
                  <Pressable
                    onPress={handleRestoreBackup}
                    disabled={isLoading}
                    className="bg-blue-500 px-12 py-6 rounded-full">
                    <BaseText fontSize={14} weight="medium" className="text-white">
                      {isLoading ? 'Loading...' : 'Restore'}
                    </BaseText>
                  </Pressable>
                ),
              },
            ]
          : []),
      ],
    },
    {
      title: 'App Settings',
      items: [
        {
          title: 'App Lock',
          description: 'Require face ID to open App',
          icon: (
            <Icon>
              <FingerprintIcon size={wp(18)} color={colors.grey[700]} />
            </Icon>
          ),
          rightElement: <Switch />,
        },
        {
          title: 'Permissions',
          description: 'Enable Microphones, Camera and Locations.',
          icon: (
            <Icon>
              <FileShieldIcon size={wp(18)} color={colors.grey[700]} />
            </Icon>
          ),
        },
      ],
    },
    {
      title: 'Legal',
      items: [
        {
          title: 'Provide Feedback',
          description: 'We would love to hear from you ❤️',
          icon: (
            <Icon>
              <ChatIcon size={wp(18)} color={colors.grey[700]} />
            </Icon>
          ),
        },
        {
          title: 'Terms of Service ',
          icon: (
            <Icon>
              <NotesIcon size={wp(18)} color={colors.grey[700]} />
            </Icon>
          ),
        },
        {
          title: 'Privacy Policy',
          icon: (
            <Icon>
              <NotesIcon size={wp(18)} color={colors.grey[700]} />
            </Icon>
          ),
          rightElement: <View />,
        },
      ],
    },
  ];

  return (
    <DashboardLayout insetTop={false} showLoader={false}>
      <View className="flex-1  bg-grey-25">
        <BlurView intensity={10} className="absolute top-0 right-0 left-0 w-full px-16 pb-10 z-[10]">
          <View className="h-7 w-50 rounded-full bg-grey-50 self-center my-14" />
          <Row>
            <BaseText fontSize={24} weight="medium" className="text-grey-600 text-center">
              Settings
            </BaseText>
            <Pressable onPress={() => navigation.goBack()}>
              <CircledIcon className="bg-grey-50 py-6 px-12">
                <CancelIcon size={wp(18)} color={colors.grey[300]} />
              </CircledIcon>
            </Pressable>
          </Row>
        </BlurView>
        <ScrollView className="flex-1">
          <View className="pt-80 px-16">
            {settings.map((section, idx) => (
              <SettingSection key={idx}>
                {section.items.map((item, idx) => (
                  <SettingItem
                    key={idx}
                    icon={item?.icon}
                    title={item.title}
                    showBorder={idx !== section.items.length - 1}
                    description={item?.description}
                    rightElement={item?.rightElement}
                  />
                ))}
              </SettingSection>
            ))}
          </View>
        </ScrollView>
      </View>
    </DashboardLayout>
  );
};

export default SettingsScreen;

const SettingItem = ({
  icon,
  title,
  showBorder,
  description,
  rightElement,
}: {
  icon: ReactNode;
  title: string;
  description?: string;
  showBorder?: boolean;
  rightElement: React.ReactNode;
}) => {
  return (
    <Row className="pl-12">
      <View className="py-10">{icon}</View>
      <View
        className={`flex-1 flex-row items-center h-full px-10 pr-12 ${
          showBorder && 'border-b-hairline border-[#54545657]'
        }`}>
        <View className="flex-1">
          {title && (
            <BaseText fontSize={16} weight="medium" className="text-grey-700">
              {title}
            </BaseText>
          )}
          {description && (
            <BaseText fontSize={14} weight="medium" className="text-grey-200" numberOfLines={1}>
              {description}
            </BaseText>
          )}
        </View>
        {rightElement}
      </View>
    </Row>
  );
};

const SettingSection = ({ children }: { children: React.ReactNode }) => {
  return <View className="bg-white rounded-10 mb-12">{children}</View>;
};

const Icon = ({ children }: { children: React.ReactNode }) => {
  return (
    <View className="bg-[#F4F4F4] rounded-full p-3">
      <View className="rounded-full p-8 border-2 border-dashed border-grey-200">{children}</View>
    </View>
  );
};
