import DashboardLayout from '@components/ui/layout/dashboard-layout';
import BottomTab from '@components/ui/layout/bottom-tab';
import useStatusbar from '@hooks/use-statusbar';
import HomeHeader from '@components/home/<USER>';
import TodaySection from '@components/home/<USER>';
import { View } from 'react-native';
import HomeEmptyState from '@components/home/<USER>';
import DayEntriesSection from '@components/home/<USER>';
import Animated, {
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useJournal } from '@hooks/use-journal-db-service';
import { useCallback, useEffect } from 'react';
import SearchOverlay from '@components/home/<USER>/search-overlay';
import { JournalEntry } from '@services/db/schema';
import { useSelectionStore } from '@store/useSelectionStore';
import { useFocusEffect } from '@react-navigation/native';

const HomeScreen = () => {
  const { toggleEntrySelection, clearSelection, selectedEntries } = useSelectionStore();
  const { loadEntries, entries, isDbInitialized } = useJournal();

  useEffect(() => {
    loadEntries();
  }, [isDbInitialized, selectedEntries]);

  useFocusEffect(
    useCallback(() => {
      loadEntries();
      return () => {};
    }, [isDbInitialized]),
  );

  useStatusbar('dark');
  const scrollYPosition = useSharedValue(0);
  const isSearchState = useSharedValue(false);
  const isSelectionActive = useSharedValue(false);

  const isEmpty = entries.length === 0;

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      'worklet';
      const currentScrollY = event.contentOffset.y;
      scrollYPosition.value = currentScrollY;
    },
    onEndDrag: event => {
      'worklet';
      const currentScrollY = event.contentOffset.y;
      scrollYPosition.value = currentScrollY;
      if (currentScrollY < -70) {
        isSearchState.value = true;
      }
    },
  });

  const homeBaseStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isSearchState.value ? 0 : 1, { duration: 250 }),
      // display: isSearchState.value ? 'none' : 'flex',
      // opacity: withSpring(isSearchState.value ? 0 : 1, { damping: 18, stiffness: 200, velocity: 4 }),
    };
  }, []);

  const insets = useSafeAreaInsets();

  const onPressSelectionActive = () => {
    isSelectionActive.value = !isSelectionActive.value;
    clearSelection();
  };

  const handleSelectEntry = (entry: string) => {
    toggleEntrySelection(entry);
  };

  return (
    <DashboardLayout insetTop={false}>
      <View className="flex-1">
        <SearchOverlay isSearchState={isSearchState} />
        <Animated.View style={homeBaseStyle} className="flex-1">
          <View style={{ display: !isEmpty ? 'flex' : 'none' }} className="flex-1">
            <Animated.ScrollView
              testID="animated-scroll-view"
              className="flex-1"
              stickyHeaderIndices={[0]}
              scrollEventThrottle={16}
              onScroll={scrollHandler}>
              <HomeHeader isSelectionActive={isSelectionActive} onPressSelectionActive={onPressSelectionActive} />
              <View className="flex-1 pb-120">
                <TodaySection isSelectionActive={isSelectionActive} handleSelectEntry={handleSelectEntry} />
                <DayEntriesSection isSelectionActive={isSelectionActive} handleSelectEntry={handleSelectEntry} />
              </View>
            </Animated.ScrollView>
          </View>
          <View style={{ display: isEmpty ? 'flex' : 'none' }} className="flex-1  justify-end">
            <HomeEmptyState />
          </View>
          <BottomTab isSelectionActive={isSelectionActive} handleSelectEntry={handleSelectEntry} />
        </Animated.View>
      </View>
    </DashboardLayout>
  );
};

export default HomeScreen;
