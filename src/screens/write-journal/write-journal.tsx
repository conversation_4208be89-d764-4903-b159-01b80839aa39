import { Dimensions, ScrollView, View } from 'react-native';
import { BaseText } from '@components/ui/base';
import DashboardLayout from '@components/ui/layout/dashboard-layout';
import useStatusbar from '@hooks/use-statusbar';
import Row from '@components/ui/layout/row';
import { ChevronSelectorVerticalIcon, ExpandIcon } from '@components/ui/others/icons';
import { hp, wp } from '@utils/responsive-dimension';
import { colors } from '@theme/colors';
import { MoodIcon } from '@components/ui/others/mood-icon';
import { MOOD_VARIANT } from 'src/@types/app-interface';
import Pressable from '@components/ui/base/pressable';
import HighlightedText from '@components/ui/others/highlighted-text';
import dayjs from 'dayjs';
import { TextInput } from 'react-native';
import BottomToolbox from '@components/write-journal/bottom-toolbox';
import AvoidKeyboard from '@components/ui/layout/avoid-keyboard';
import SelectMoodModal from '@components/ui/modals/select-mood-modal';
import { useCallback, useEffect, useState } from 'react';
import { LocationSearchResult } from '@hooks/use-location-search';
import DateTimeSelectorModal from '@components/ui/modals/date-time-selector-modal';
import { useNavigation } from '@react-navigation/native';
import { useForm, useStore } from '@tanstack/react-form';
import { InsertJournalEntry } from '@services/db/schema';
import useModals from '@hooks/use-modals';
import Animated, {
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useJournal } from '@hooks/use-journal-db-service';
import useRouteParams from '@hooks/use-route-params';
import ImageCard, { MiniImageCard } from '@components/cards/image-card';
import { formatDate } from '@utils/functions';
import { ArrowDown2 } from 'iconsax-react-native';
import { BlurView } from 'expo-blur';
import CustomImage from '@components/ui/others/custom-image';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const screen = Dimensions.get('screen');

type DefaultEntryType = Omit<InsertJournalEntry, 'mood' | 'updated_date' | 'created_date'> & { mood: MOOD_VARIANT };

const defaultEntry: DefaultEntryType = {
  mood: MOOD_VARIANT.NONE,
  title: '',
  content: '',
  images: [],
  location: null,
  id: undefined,
  bookmarked: false,
  entry_date: new Date().toISOString(),
};

const WriteJournalScreen = () => {
  const { modals, toggleModal } = useModals(['mood', 'location', 'date']);
  useStatusbar(modals.location ? 'light' : 'dark');
  const params = useRouteParams<'WriteJournalScreen'>();
  const { addEntry, editEntry, getEntry, loadEntries } = useJournal();

  const isEdit = Boolean(params?.journalId);

  const showToolBox = useSharedValue(0);
  const isEditValue = useSharedValue(isEdit);
  const showLoader = useSharedValue(true);

  useEffect(() => {
    setTimeout(() => {
      showToolBox.value = 1;
    }, 500);
  }, [showToolBox]);

  const mainContainerStyle = useAnimatedStyle(() => ({
    ...(isEditValue.value
      ? {
          opacity: isEditValue.value && showLoader.value === false ? withTiming(1) : withTiming(0, { duration: 400 }),
          transform: [
            {
              scale: isEditValue.value && showLoader.value === false ? withTiming(1, { duration: 400 }) : 0.5,
            },
          ],
        }
      : {}),
  }));

  const containerStyle = useAnimatedStyle(() => ({
    // opacity: showToolBox.value === 1 ? 1 : 0,
    ...(isEditValue.value
      ? {}
      : {
          transform: [
            {
              translateY: showToolBox.value === 1 ? withSpring(0, { damping: 20, stiffness: 200, velocity: 15 }) : -250,
            },
          ],
        }),
  }));

  const loadingAnimatedStyle = useAnimatedStyle(() => ({
    opacity: isEditValue.value && showLoader.value ? withTiming(1) : withTiming(0, { duration: 600 }),
    transform: [
      {
        scale: isEditValue.value && showLoader.value ? 1 : withTiming(1.5, { duration: 400 }),
      },
    ],
  }));

  const form = useForm({
    defaultValues: defaultEntry,
    onSubmit: ({ value }) => {
      alert(JSON.stringify(value, null, 2));
    },
  });

  const formValues = useStore(form.store).values;
  const navigation = useNavigation();

  useEffect(() => {
    const getEntryWithId = async () => {
      if (params?.journalId) {
        const entry = await getEntry(params?.journalId);
        if (!entry) return;
        Object.entries(entry).forEach(e => {
          form.setFieldValue(e[0]! as keyof typeof defaultEntry, e[1]);
        });
        setTimeout(() => {
          showLoader.value = false;
        }, 1500);
      }
    };

    getEntryWithId();
  }, [form, getEntry, params?.journalId]);

  useEffect(() => {
    const saveEntry = async () => {
      if (formValues.title && formValues.content) {
        if (formValues.id) {
          editEntry(formValues.id, formValues);
          return;
        }
        const journalID = await addEntry(formValues);
        form.setFieldValue('id', journalID!);
      }
    };
    saveEntry();
  }, [formValues]);

  const handleMoodSelect = (mood: MOOD_VARIANT) => {
    form.setFieldValue('mood', mood);
    toggleModal('mood', false);
  };

  const currentDate = formValues?.entry_date;

  const handleLocationSelect = async (location: LocationSearchResult) => {
    form.setFieldValue('location', location);
    toggleModal('location', false);
  };

  const handleDateTimeSelect = useCallback(
    (newDate?: Date, newTime?: Date) => {
      if (newDate) {
        // Preserve existing time when updating date
        const updatedDateTime = dayjs(newDate)
          .hour(dayjs(currentDate).hour())
          .minute(dayjs(currentDate).minute())
          .toDate();

        form.setFieldValue('entry_date', updatedDateTime.toISOString());
      }

      if (newTime) {
        // Preserve existing date when updating time
        const updatedDateTime = dayjs(currentDate).hour(dayjs(newTime).hour()).minute(dayjs(newTime).minute()).toDate();
        form.setFieldValue('entry_date', updatedDateTime.toISOString());
      }
    },
    [currentDate],
  );

  const handleImages = (images: string[]) => {
    const currentImages = formValues?.images || [];
    form.setFieldValue('images', [...currentImages, ...images]);
  };

  const dateFormatted = `${formatDateWithToday(formValues?.entry_date!)} *at* ${dayjs(formValues?.entry_date!).format(
    'HH:mm',
  )}`;

  const hasLocation = Boolean(formValues?.location?.address);

  const insetTop = useSafeAreaInsets().top;

  return (
    <DashboardLayout insetTop={false} showLoader={false}>
      <Animated.View className="flex-1" style={mainContainerStyle}>
        <JournalHeader
          dateFormatted={dateFormatted}
          mood={formValues?.mood}
          isEditValue={isEditValue}
          onBackPress={() => {
            loadEntries();
            navigation.goBack();
          }}
          onDatePress={() => toggleModal('date')}
          onMoodPress={() => toggleModal('mood')}
          title={formValues?.title}
          onTitleChange={text => form.setFieldValue('title', text)}
          location={formValues?.location}
        />
        <AvoidKeyboard keyboardVerticalOffset={hp(40)}>
          <Animated.View className="flex-1" style={containerStyle}>
            {/* {formValues?.location?.address && (
              <BaseText fontSize={14} className="text-grey-300 pt-10 px-16">
                {formValues?.location?.address}
              </BaseText>
            )}
            <TextInput
              placeholder="Title"
              value={formValues.title}
              className="font-circularMedium text-grey-800 mt-12 px-16"
              placeholderTextColor={colors.grey[300]}
              keyboardAppearance="default"
              style={{ fontSize: wp(20), lineHeight: hp(22) }}
              returnKeyType="done"
              onChangeText={text => form.setFieldValue('title', text)}
            /> */}
            <ScrollView
              keyboardShouldPersistTaps="handled"
              keyboardDismissMode="on-drag"
              className="flex-1"
              style={{ paddingTop: hasLocation ? insetTop * 2.45 : insetTop * 2.05 }}>
              <TextInput
                placeholder="Write how you feel"
                multiline
                keyboardAppearance="default"
                scrollEnabled={false}
                value={formValues.content}
                className="flex-1 font-circularBook text-grey-400 mt-10 px-16 pb-100"
                placeholderTextColor={colors.grey[200]}
                style={{ fontSize: wp(16), lineHeight: hp(22), textAlignVertical: 'top' }}
                onChangeText={text => form.setFieldValue('content', text)}
              />
            </ScrollView>
            {/* {formValues?.images && (
              <View className="px-40 -mt-40 z-20">
                <MiniImageCard images={formValues?.images} />
              </View>
            )} */}
          </Animated.View>
        </AvoidKeyboard>
        <BottomToolbox
          selectedLocationDetail={formValues?.location!}
          onLocationSelect={handleLocationSelect}
          isEditValue={isEditValue}
          isLocationModalVisible={modals.location}
          setIsLocationModalVisible={v => toggleModal('location', v)}
          onImageSelect={(img: string[]) => handleImages(img)}
          onCameraCapture={(img: string) => handleImages([img])}
          isBookmarked={formValues?.bookmarked!}
          onBookmarkToggle={() => form.setFieldValue('bookmarked', !formValues.bookmarked)}
        />
      </Animated.View>
      <SelectMoodModal
        isVisible={modals.mood}
        closeModal={() => toggleModal('mood', false)}
        selectedMood={formValues?.mood}
        onSelectMood={handleMoodSelect}
      />
      <DateTimeSelectorModal
        isVisible={modals.date}
        closeModal={() => toggleModal('date', false)}
        onDateTimeSelect={handleDateTimeSelect}
        initialDate={new Date(formValues?.entry_date!)}
        initialTime={new Date(formValues?.entry_date!)}
      />

      <Animated.View
        className="flex-1 bg-white absolute w-full h-full"
        style={[{ height: screen.height }, loadingAnimatedStyle]}>
        <View className="flex-1 items-center justify-center z-20">
          {params?.images && <MiniImageCard images={params?.images} />}
          <View className="">
            <BaseText fontSize={16} className="text-center text-grey-500">
              {dayjs(params?.entry_date).format('ddd')}
            </BaseText>
            <BaseText fontSize={20} lineHeight={22} weight="bold" className="text-center text-grey-500 mt-3">
              {dayjs(params?.entry_date).format('DD').toUpperCase()}
            </BaseText>
            <BaseText fontSize={16} weight="medium" className="text-center text-grey-500 mt-10">
              {params?.title}
            </BaseText>
          </View>
          <Row disableSpread style={{ gap: wp(5) }} className="mt-4">
            <BaseText numberOfLines={1} fontSize={14} lineHeight={22} className="text-grey-300">
              {formatDate(params?.entry_date, 'MMM YYYY').toUpperCase()}
            </BaseText>
            <View className="h-5 w-5 rounded-full bg-[#D9D9D9]" />
            <BaseText numberOfLines={1} fontSize={14} lineHeight={22} className="text-grey-300">
              {formatDate(params?.entry_date, 'hh:mm A')}
            </BaseText>
          </Row>
        </View>
      </Animated.View>
    </DashboardLayout>
  );
};

export default WriteJournalScreen;

function formatDateWithToday(date: string, customFormat = 'D MMM YYYY') {
  const selectedDate = dayjs(new Date(date));

  if (selectedDate.isSame(dayjs(), 'day')) {
    return 'Today';
  } else {
    return selectedDate.format(customFormat);
  }
}

interface JournalHeaderProps {
  dateFormatted: string;
  mood: MOOD_VARIANT;
  onBackPress: () => void;
  isEditValue?: SharedValue<boolean>;
  onDatePress: () => void;
  onMoodPress: () => void;
  title: string;
  onTitleChange: (text: string) => void;
  location:
    | {
        latitude: number;
        longitude: number;
        address: string;
        name: string;
      }
    | null
    | undefined;
}

const JournalHeader = ({
  dateFormatted,
  mood,
  onBackPress,
  isEditValue,
  onDatePress,
  onMoodPress,
  title,
  onTitleChange,
  location,
}: JournalHeaderProps) => {
  const showToolBox = useSharedValue(0);

  useEffect(() => {
    setTimeout(() => {
      showToolBox.value = 1;
    }, 500);
  }, [showToolBox]);

  const containerStyle = useAnimatedStyle(() => ({
    // opacity: showToolBox.value === 1 ? 1 : 0,
    ...(isEditValue?.value
      ? {}
      : {
          transform: [
            {
              translateY: showToolBox.value === 1 ? withSpring(0, { damping: 20, stiffness: 200, velocity: 15 }) : -250,
            },
          ],
        }),
  }));

  const insetTop = useSafeAreaInsets().top;

  return (
    <Animated.View
      className="absolute top-0 right-0 left-0 w-full z-10"
      style={[containerStyle, { paddingTop: insetTop }]}>
      <View className="absolute bottom-0 right-0 left-0 top-0 w-full h-full">
        <CustomImage
          imageProps={{
            source: require('@assets/images/blur-bg-top.png'),
            contentFit: 'cover',
          }}
          className="w-full h-full"
        />
      </View>
      <Row className="px-16 pb-13">
        <Pressable className="rounded-full items-center justify-center bg-grey-50 py-6 px-12" onPress={onBackPress}>
          <ExpandIcon size={wp(18)} color={colors.grey[300]} />
        </Pressable>
        <Pressable className="flex-1 flex-row justify-center" onPress={onDatePress}>
          <HighlightedText
            className="text-center"
            fontSize={18}
            lineHeight={22}
            highlightedColor={colors.grey[100]}
            notHighlightedColor={colors.grey[300]}
            text={dateFormatted}
          />
          <ChevronSelectorVerticalIcon size={wp(18)} color={colors.grey[300]} />
        </Pressable>
        <MoodIcon mood={mood} onPress={onMoodPress} />
      </Row>
      {location?.address && (
        <BaseText fontSize={14} className="text-grey-300 pt-10 px-16">
          {location?.address}
        </BaseText>
      )}
      <TextInput
        placeholder="Title"
        value={title}
        className="font-circularMedium text-grey-800 mt-12 px-16"
        placeholderTextColor={colors.grey[300]}
        keyboardAppearance="default"
        style={{ fontSize: wp(20), lineHeight: hp(22) }}
        returnKeyType="done"
        onChangeText={onTitleChange}
      />
    </Animated.View>
  );
};
