import { View } from 'react-native';
import { BaseText } from '@components/ui/base';
import DashboardLayout from '@components/ui/layout/dashboard-layout';
import BottomTab from '@components/ui/layout/bottom-tab';
import { ScrollView } from 'react-native-gesture-handler';
import useStatusbar from '@hooks/use-statusbar';

const DashboardScreen = () => {
  const {setStatusBar} = useStatusbar('light',);
  // setStatusBar('light', '#fff', true);

  return (
    <DashboardLayout insetTop={false}>
      <ScrollView className="flex-1 p-16">
        <BaseText 
          fontSize={16} 
          weight="medium" 
          className="text-grey-600 text-center"
        >
          Dashboard
        </BaseText>
      </ScrollView>
    </DashboardLayout>
  );
};

export default DashboardScreen;