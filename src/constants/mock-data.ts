import { MOOD_VARIANT } from "src/@types/app-interface";

export const journalData = [
  {
    id: "j1006",
    mood: MOOD_VARIANT.ANGRY,
    title: "Car trouble again",
    content: "Third time this month my car's check engine light came on. Took it to the mechanic who said the previous repair wasn't done properly. Now I have to pay again for something that should have been fixed right the first time! Had a heated conversation with the service manager, but they're refusing to cover it under warranty. Might need to find a new mechanic and maybe even a lawyer.",
    images: ["https://picsum.photos/1024", "https://picsum.photos/1024", "https://picsum.photos/1024", "https://picsum.photos/1024"],
    created_date: "2025-03-28T14:15:00Z",
    updated_date: "2025-03-28T15:20:00Z"
  },
  {
    id: "j1004",
    mood: MOOD_VARIANT.CONFUSED,
    title: "Career crossroads",
    content: "Got offered a job at a startup today. Better pay, more responsibility, but higher risk than my current position. I've been at my company for four years now - it's comfortable and stable. The startup role sounds exciting but scary. Been making pros and cons lists all day and still can't decide. Need to give them an answer by Friday. What should I prioritize at this point in my career?",
    images: ["https://picsum.photos/1024", "https://picsum.photos/1024"],
    created_date: "2025-04-04T16:20:00Z", 
    updated_date: "2025-04-04T18:45:00Z"
  },
  {
    id: "j1003",
    mood: MOOD_VARIANT.NERVOUS,
    title: "Big presentation tomorrow",
    content: "Can't sleep. Keep going over my slides for tomorrow's client presentation. This account is so important for the company, and my boss has trusted me to lead this pitch. I've prepared as much as I can, but my stomach is in knots. Going to try some meditation exercises and get to bed early. Deep breaths...",
    images: [],
    created_date: "2025-04-03T23:10:00Z",
    updated_date: "2025-04-03T23:10:00Z"
  },
  {
    id: "j1001",
    mood: MOOD_VARIANT.HAPPY,
    title: "Amazing day at the beach",
    content: "Today was incredible! The sun was shining, the waves were perfect, and I spent the whole day with friends. We had a picnic, played volleyball, and stayed until sunset. I felt so relaxed and content - exactly what I needed after a stressful week. Mental note: beach days should be a monthly ritual.",
    images: ["https://picsum.photos/1024", "https://picsum.photos/1024", "https://picsum.photos/1024"],
    created_date: "2025-04-01T09:30:00Z",
    updated_date: "2025-04-01T09:30:00Z"
  },
  {
    id: "j1002",
    mood: MOOD_VARIANT.SAD,
    title: "Missing home",
    content: "Feeling a bit down today. It's been three months since I moved to the new city, and while things are going well at work, I really miss my family and old friends. Had a long call with mom this evening which helped a little, but the apartment feels empty. Maybe I should join that local hiking group this weekend to meet some new people.",
    images: ["https://picsum.photos/1024"],
    created_date: "2025-04-02T19:45:00Z",
    updated_date: "2025-04-02T20:15:00Z"
  },
  {
    id: "j1003",
    mood: MOOD_VARIANT.NERVOUS,
    title: "Big presentation tomorrow",
    content: "Can't sleep. Keep going over my slides for tomorrow's client presentation. This account is so important for the company, and my boss has trusted me to lead this pitch. I've prepared as much as I can, but my stomach is in knots. Going to try some meditation exercises and get to bed early. Deep breaths...",
    images: [],
    created_date: "2025-04-03T23:10:00Z",
    updated_date: "2025-04-03T23:10:00Z"
  },
  {
    id: "j1004",
    mood: MOOD_VARIANT.CONFUSED,
    title: "Career crossroads",
    content: "Got offered a job at a startup today. Better pay, more responsibility, but higher risk than my current position. I've been at my company for four years now - it's comfortable and stable. The startup role sounds exciting but scary. Been making pros and cons lists all day and still can't decide. Need to give them an answer by Friday. What should I prioritize at this point in my career?",
    images: ["https://picsum.photos/1024", "https://picsum.photos/1024"],
    created_date: "2025-04-04T16:20:00Z", 
    updated_date: "2025-04-04T18:45:00Z"
  },
  {
    id: "j1005",
    mood: MOOD_VARIANT.NEUTRAL,
    title: "Ordinary Tuesday",
    content: "Nothing special today. Work was the usual routine - meetings, emails, lunch at my desk. Picked up groceries on the way home. Started watching that new series everyone's talking about, but only got through one episode before feeling tired. Sometimes these ordinary days are comforting in their predictability.",
    images: ["https://picsum.photos/1024"],
    created_date: "2025-04-05T21:30:00Z",
    updated_date: "2025-04-05T21:30:00Z"
  },
  {
    id: "j1007",
    mood: MOOD_VARIANT.SMILE,
    title: "Family reunion",
    content: "Just got back from the weekend family reunion. It was chaos in the best possible way - cousins I haven't seen in years, grandma's legendary cooking, old stories being retold and embellished as always. We looked through old photo albums and I heard some family history I'd never known before. Already looking forward to next year's gathering. Need to remember to get Aunt Carol's cookie recipe!",
    images: ["https://picsum.photos/1024", "https://picsum.photos/1024", "https://picsum.photos/1024", "https://picsum.photos/1024", "https://picsum.photos/1024", "https://picsum.photos/1024"],
    created_date: "2025-03-25T22:40:00Z",
    updated_date: "2025-03-26T09:10:00Z"
  },
  {
    id: "j1008",
    mood: MOOD_VARIANT.MELT,
    title: "Exhausted beyond words",
    content: "This week has completely drained me. The project deadline was moved up, so I've been pulling 12-hour days at the office. My apartment is a mess, I've been eating takeout for every meal, and I can't remember the last time I properly exercised. Going to take a long bath, turn off my phone, and sleep for the entire weekend. Monday can wait.",
    images: ["https://picsum.photos/1024"],
    created_date: "2025-03-22T23:55:00Z",
    updated_date: "2025-03-23T00:05:00Z"
  },
  {
    id: "j1009",
    mood: MOOD_VARIANT.CRY,
    title: "Saying goodbye",
    content: "Had to put my cat Whiskers down today. She was 18 years old and has been with me through college, three moves, and countless life changes. The vet was very kind and said it was the right decision given her quality of life, but I can't stop crying. The apartment feels so empty without her little sounds and presence. I know time will help, but right now the grief is overwhelming.",
    images: ["https://picsum.photos/1024", "https://picsum.photos/1024"],
    created_date: "2025-03-19T12:30:00Z",
    updated_date: "2025-03-19T17:45:00Z"
  },
  {
    id: "j1010",
    mood: MOOD_VARIANT.NONE,
    title: "Reflection day",
    content: "Spent the day at the retreat center, completely unplugged. No phone, no internet, just journaling, meditation, and walks in nature. It's amazing how much mental clarity comes when you remove all the distractions. I've been so caught up in the daily grind that I haven't taken time to check in with myself and my longer-term goals. Going to try making this a monthly practice.",
    images: [],
    created_date: "2025-03-15T18:20:00Z",
    updated_date: "2025-03-15T18:20:00Z"
  }
];