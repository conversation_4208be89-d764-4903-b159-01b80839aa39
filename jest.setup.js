// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  
  // Mock useSharedValue hook
  Reanimated.useSharedValue = jest.fn((initialValue) => ({ value: initialValue }));
  
  // Mock useAnimatedStyle hook
  Reanimated.useAnimatedStyle = jest.fn((callback) => callback());
  
  // Mock animation functions
  Reanimated.withTiming = jest.fn((toValue) => toValue);
  Reanimated.withSpring = jest.fn((toValue) => toValue);
  
  // Mock useAnimatedScrollHandler
  Reanimated.useAnimatedScrollHandler = jest.fn((handlers) => {
    return (event) => {
      if (handlers.onScroll) handlers.onScroll(event);
      if (handlers.onEndDrag) handlers.onEndDrag(event);
    };
  });
  
  return {
    ...Reanimated,
    default: {
      ...Reanimated,
    },
  };
});

// Mock other global dependencies
jest.mock('react-native-gesture-handler', () => {
  const RN = require('react-native');
  return {
    ScrollView: RN.ScrollView,
    TouchableOpacity: RN.TouchableOpacity,
    // Add other components as needed
  };
});

jest.mock('react-native-safe-area-context', () => {
  const insets = { top: 0, right: 0, bottom: 0, left: 0 };
  return {
    SafeAreaProvider: ({ children }) => children,
    SafeAreaView: ({ children }) => children,
    useSafeAreaInsets: jest.fn(() => insets),
  };
});

// Add global mocks for your custom components
// This is optional but helpful to isolate component tests
global.mockComponent = (componentName) => {
  return function MockComponent(props) {
    return React.createElement(componentName, {
      ...props,
      testID: props.testID || componentName,
    });
  };
};