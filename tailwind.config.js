/** @type {import('tailwindcss').Config} */

const {colors} = require("./src/theme/colors");

module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ["./src/**/*.{js,jsx,ts,tsx}", "*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    fontFamily: {
      circularBook: ["CircularStd-Book"],
      circularBold: ["CircularStd-Bold"],
      circularBlack: ["CircularStd-Black"],
      circularLight: ["CircularStd-Light"],
      circularMedium: ["CircularStd-Medium"],
    },
    colors: {
      ...colors,
    },
    extend: {
      zIndex: {
        1: 1,
        2: 2,
        3: 3,
        4: 4,
        5: 5,
        6: 6,
        7: 7,
        8: 8,
        9: 9,
      },
      spacing: {
        1: "1px",
        2: "2px",
        3: "3px",
        4: "4px",
        5: "5px",
        6: "6px",
        7: "7px",
        8: "8px",
        9: "9px",
        10: "10px",
        12: "12px",
        14: "14px",
        15: "15px",
        16: "16px",
        18: "18px",
        20: "20px",
        24: "24px",
        25: "25px",
        26: "26px",
        27: "27px",
        28: "28px",
        30: "30px",
        32: "32px",
        35: "35px",
        36: "36px",
        40: "40px",
        45: "45px",
        48: "48px",
        50: "50px",
        52: "52px",
        70: "70px",
        75: "75px",
        80: "80px",
        100: "100px",
        110: "110px",
        120: "120px",
      },
      borderRadius: {
        2: "2px",
        4: "4px",
        5: "5px",
        6: "6px",
        8: "8px",
        10: "10px",
        12: "12px",
        15: "15px",
        16: "16px",
        20: "20px",
        30: "30px",
        40: "40px",
      },
    },
  },
  plugins: [],
};
