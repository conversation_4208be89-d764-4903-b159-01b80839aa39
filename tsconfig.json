{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "module": "es6",
    "skipLibCheck": true /* <PERSON>p type checking all .d.ts files. */,
    "baseUrl": "." /* Specify the base directory to resolve non-relative module names. */,
    "paths": {
      // "*": ["./*"],
      // "@/*": ["src/*"],
      "@services/*": ["src/services/*"],
      "@components/*": ["src/components/*"],
      "@assets/*": ["src/assets/*"],
      "@screens/*": ["src/screens/*"],
      "@theme/*": ["src/theme/*"],
      "@store/*": ["src/store/*"],
      "@redux/*": ["src/redux/*"],
      "@utils/*": ["src/utils/*"],
      "@navigation/*": ["src/navigation/*"],
      "@hooks/*": ["src/hooks/*"],
      "@constant/*": ["src/constant/*"]
    },
  },
}
