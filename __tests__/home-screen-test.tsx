import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import HomeScreen from '@screens/home/<USER>'; // Adjust path as needed

// Mock your component dependencies
jest.mock('@components/ui/layout/dashboard-layout', () => 'DashboardLayout');
jest.mock('@components/ui/layout/bottom-tab', () => 'BottomTab');
jest.mock('@components/home/<USER>', () => 'HomeHeader');
jest.mock('@components/home/<USER>', () => 'TodaySection');
jest.mock('@components/home/<USER>', () => 'DayEntriesSection');
jest.mock('@components/home/<USER>', () => 'HomeEmptyState');
jest.mock('@components/ui/layout/row', () => 'Row');
jest.mock('@components/ui/layout/avoid-keyboard', () => 'AvoidKeyboard');
jest.mock('@components/ui/others/custom-image', () => 'CustomImage');
jest.mock('@components/ui/base', () => ({
  BaseText: 'BaseText',
}));

// Mock hooks
jest.mock('@hooks/use-statusbar', () => jest.fn());

// Mock utility functions and constants
jest.mock('@utils/responsive-dimension', () => ({
  wp: jest.fn(val => val),
}));

jest.mock('@theme/colors', () => ({
  colors: {
    grey: {
      100: '#e1e1e1',
      300: '#b3b3b3', 
      400: '#999999',
      700: '#333333',
    },
  },
}));

// Mock button component with testID for easier testing
jest.mock('@components/ui/buttons/button', () => {
  const MockButton = ({ text, onPress }) => (
    <div data-testid={`button-${text}`} onPress={onPress}>{text}</div>
  );
  
  MockButton.displayName = 'Button';
  
  return {
    __esModule: true,
    default: MockButton,
    ButtonVariant: {
      LIGHT: 'light',
    },
  };
});

// Icon mocks
jest.mock('@components/ui/others/icons', () => ({
  SearchIcon: 'SearchIcon',
  CancelIcon: 'CancelIcon',
  BookmarkIcon: 'BookmarkIcon',
}));

describe('HomeScreen Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText, queryByText } = render(<HomeScreen />);
    
    // Test that main components are rendered
    expect(queryByText('HomeHeader')).toBeTruthy();
    expect(queryByText('TodaySection')).toBeTruthy();
    expect(queryByText('BottomTab')).toBeTruthy();
  });

  it('switches to search mode on pull down', async () => {
    // Get access to the shared value mock from our setup
    const { useSharedValue } = require('react-native-reanimated');
    
    // For this test, we'll directly manipulate the mock's return value
    // to simulate a state change rather than trying to simulate the scroll gesture
    const mockSharedValue = { value: false };
    useSharedValue.mockImplementation((initialValue) => {
      if (initialValue === false) {
        return mockSharedValue; // Return our controlled mock for isSearchState
      }
      return { value: initialValue }; // Return default for other useSharedValue calls
    });
    
    const { getByPlaceholderText, queryByPlaceholderText } = render(<HomeScreen />);
    
    // Initially search input should not be visible/focused
    expect(queryByPlaceholderText('Search for your words')).toBeNull();
    
    // Simulate search state activation
    act(() => {
      mockSharedValue.value = true;
    });
    
    // Wait for the search input to appear
    await waitFor(() => {
      expect(queryByPlaceholderText('Search for your words')).toBeTruthy();
    });
  });

  // Add more tests as needed
});