{"expo": {"name": "writtendays", "slug": "writtendays", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"bundleIdentifier": "com.getwrittendays", "supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "Writtendays needs access to your camera to take photos.", "NSPhotoLibraryUsageDescription": "Writtendays needs access to your photos to save images.", "ITSAppUsesNonExemptEncryption": false}}, "android": {"package": "com.getwrittendays", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-font", "expo-localization", "react-native-cloud-storage", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}], ["expo-sqlite", {"enableFTS": true, "useSQLCipher": true, "android": {"enableFTS": false, "useSQLCipher": false}, "ios": {"customBuildFlags": ["-DSQLITE_ENABLE_DBSTAT_VTAB=1 -DSQLITE_ENABLE_SNAPSHOT=1"]}}]], "extra": {"eas": {"projectId": "09db66ad-c383-4a7d-af31-558385a51b88"}}}}