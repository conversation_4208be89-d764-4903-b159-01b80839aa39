module.exports = {
  preset: 'jest-expo',
  transformIgnorePatterns: [
    'node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|react-native-reanimated)'
  ],
  moduleNameMapper: {
    // Handle module aliases (if you're using module aliases in your project)
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@theme/(.*)$': '<rootDir>/src/theme/$1',
    '^@screens/(.*)$': '<rootDir>/src/screens/$1',
    // Handle static files
    '\\.(jpg|jpeg|png|gif|webp|svg)$': 'jest-transform-stub',
    '\\.(css|less)$': 'identity-obj-proxy'
  },
  // setupFilesAfterEnv: ['@testing-library/jest-native/extend-expect'],
  // Optional: Set test environment
  testEnvironment: 'node',
  // Optional: Set coverage collection paths
  collectCoverageFrom: [
    '**/*.{js,jsx}',
    '!**/node_modules/**',
    '!**/coverage/**'
  ],
  // Optional: Add a setup file for global mocks
  setupFiles: ['<rootDir>/jest.setup.js']
};