{"name": "writtendays", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "eslint .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@instalog.dev/expo": "^0.1.0", "@react-native-community/datetimepicker": "8.2.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.3.8", "@react-navigation/stack": "^7.1.2", "@shopify/react-native-skia": "1.5.0", "@tanstack/react-form": "^1.9.0", "babel-plugin-inline-import": "^3.0.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "drizzle-orm": "^0.43.1", "expo": "~52.0.38", "expo-blur": "~14.0.3", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-drizzle-studio-plugin": "^0.1.2", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-image": "~2.0.7", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-local-authentication": "~15.0.2", "expo-localization": "~16.0.1", "expo-location": "~18.0.10", "expo-maps": "^0.7.3", "expo-sensors": "~14.0.2", "expo-splash-screen": "~0.29.22", "expo-sqlite": "~15.1.4", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "iconsax-react-native": "^0.0.8", "nanoid": "^5.1.5", "nativewind": "^4.1.23", "react": "18.3.1", "react-native": "0.76.7", "react-native-cloud-storage": "^2.3.0", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-mmkv": "^3.3.3", "react-native-modal": "^14.0.0-rc.1", "react-native-reanimated": "3.16.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.5.0", "react-native-webview": "13.12.5", "rive-react-native": "^9.2.0", "tailwindcss": "^3.4.17", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~18.3.12", "chokidar": "^4.0.3", "drizzle-kit": "^0.31.0", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "jest": "~29.7.0", "jest-expo": "~52.0.6", "typescript": "^5.3.3"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}